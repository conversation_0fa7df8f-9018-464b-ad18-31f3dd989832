{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/cast-stonev2/cast-stonev2/Frontend/cast-stone-frontend/src/services/config/apiConfig.ts"], "sourcesContent": ["// API Configuration\nexport const BaseApiUrl = process.env.NEXT_PUBLIC_API_URL || 'https://localhost:7069/api';\n\n// API Response Types\nexport interface ApiResponse<T> {\n  success: boolean;\n  message: string;\n  data?: T;\n  errors?: string[];\n}\n\nexport interface PaginatedResponse<T> {\n  data: T[];\n  pageNumber: number;\n  pageSize: number;\n  totalRecords: number;\n  totalPages: number;\n  hasNextPage: boolean;\n  hasPreviousPage: boolean;\n}\n\n// HTTP Methods\nexport enum HttpMethod {\n  GET = 'GET',\n  POST = 'POST',\n  PUT = 'PUT',\n  PATCH = 'PATCH',\n  DELETE = 'DELETE'\n}\n\n// API Endpoints\nexport const ApiEndpoints = {\n  // Collections\n  Collections: {\n    Base: '/collections',\n    ById: (id: number) => `/collections/${id}`,\n    ByLevel: (level: number) => `/collections/level/${level}`,\n    Children: (id: number) => `/collections/${id}/children`,\n    Hierarchy: '/collections/hierarchy',\n    Published: '/collections/published',\n    Search: '/collections/search',\n    Filter: '/collections/filter',\n    RefreshRelationships: '/collections/refresh-relationships'\n  },\n  \n  // Products\n  Products: {\n    Base: '/products',\n    ById: (id: number) => `/products/${id}`,\n    ByCollection: (collectionId: number) => `/products/collection/${collectionId}`,\n    InStock: '/products/in-stock',\n    Featured: '/products/featured',\n    Latest: '/products/latest',\n    Search: '/products/search',\n    PriceRange: '/products/price-range',\n    UpdateStock: (id: number) => `/products/${id}/stock`,\n    Filter: '/products/filter'\n  },\n  \n  // Orders\n  Orders: {\n    Base: '/orders',\n    ById: (id: number) => `/orders/${id}`,\n    ByUser: (userId: number) => `/orders/user/${userId}`,\n    ByEmail: (email: string) => `/orders/email/${email}`,\n    ByStatus: (statusId: number) => `/orders/status/${statusId}`,\n    UpdateStatus: (id: number) => `/orders/${id}/status`,\n    Cancel: (id: number) => `/orders/${id}/cancel`,\n    Pending: '/orders/pending',\n    Recent: '/orders/recent',\n    Details: (id: number) => `/orders/${id}/details`,\n    Revenue: {\n      Total: '/orders/revenue/total',\n      Range: '/orders/revenue/range'\n    },\n    Filter: '/orders/filter'\n  },\n  \n  // Users\n  Users: {\n    Base: '/users',\n    ById: (id: number) => `/users/${id}`,\n    ByEmail: (email: string) => `/users/email/${email}`,\n    ByRole: (role: string) => `/users/role/${role}`,\n    Active: '/users/active',\n    Recent: '/users/recent',\n    Deactivate: (id: number) => `/users/${id}/deactivate`,\n    Activate: (id: number) => `/users/${id}/activate`,\n    WithOrders: (id: number) => `/users/${id}/orders`,\n    EmailExists: (email: string) => `/users/email-exists/${email}`,\n    Filter: '/users/filter'\n  },\n  \n  // Seeding\n  Seed: {\n    All: '/seed/all',\n    Statuses: '/seed/statuses',\n    AdminUser: '/seed/admin-user',\n    Collections: '/seed/collections',\n    Products: '/seed/products'\n  }\n} as const;\n\n// Request Configuration\nexport interface RequestConfig extends RequestInit {\n  params?: Record<string, string | number | boolean | undefined>;\n}\n\n// Error Types\nexport class ApiError extends Error {\n  constructor(\n    message: string,\n    public status?: number,\n    public errors?: string[]\n  ) {\n    super(message);\n    this.name = 'ApiError';\n  }\n}\n\n// Utility function to build query string\nexport const buildQueryString = (params: Record<string, any>): string => {\n  const searchParams = new URLSearchParams();\n  \n  Object.entries(params).forEach(([key, value]) => {\n    if (value !== undefined && value !== null && value !== '') {\n      if (Array.isArray(value)) {\n        value.forEach(item => searchParams.append(key, String(item)));\n      } else {\n        searchParams.append(key, String(value));\n      }\n    }\n  });\n  \n  const queryString = searchParams.toString();\n  return queryString ? `?${queryString}` : '';\n};\n\n// Default headers\nexport const defaultHeaders = {\n  'Content-Type': 'application/json',\n  'Accept': 'application/json'\n};\n"], "names": [], "mappings": "AAAA,oBAAoB;;;;;;;;;AACM;AAAnB,MAAM,aAAa,kEAAmC;AAqBtD,IAAA,AAAK,oCAAA;;;;;;WAAA;;AASL,MAAM,eAAe;IAC1B,cAAc;IACd,aAAa;QACX,MAAM;QACN,MAAM,CAAC,KAAe,CAAC,aAAa,EAAE,IAAI;QAC1C,SAAS,CAAC,QAAkB,CAAC,mBAAmB,EAAE,OAAO;QACzD,UAAU,CAAC,KAAe,CAAC,aAAa,EAAE,GAAG,SAAS,CAAC;QACvD,WAAW;QACX,WAAW;QACX,QAAQ;QACR,QAAQ;QACR,sBAAsB;IACxB;IAEA,WAAW;IACX,UAAU;QACR,MAAM;QACN,MAAM,CAAC,KAAe,CAAC,UAAU,EAAE,IAAI;QACvC,cAAc,CAAC,eAAyB,CAAC,qBAAqB,EAAE,cAAc;QAC9E,SAAS;QACT,UAAU;QACV,QAAQ;QACR,QAAQ;QACR,YAAY;QACZ,aAAa,CAAC,KAAe,CAAC,UAAU,EAAE,GAAG,MAAM,CAAC;QACpD,QAAQ;IACV;IAEA,SAAS;IACT,QAAQ;QACN,MAAM;QACN,MAAM,CAAC,KAAe,CAAC,QAAQ,EAAE,IAAI;QACrC,QAAQ,CAAC,SAAmB,CAAC,aAAa,EAAE,QAAQ;QACpD,SAAS,CAAC,QAAkB,CAAC,cAAc,EAAE,OAAO;QACpD,UAAU,CAAC,WAAqB,CAAC,eAAe,EAAE,UAAU;QAC5D,cAAc,CAAC,KAAe,CAAC,QAAQ,EAAE,GAAG,OAAO,CAAC;QACpD,QAAQ,CAAC,KAAe,CAAC,QAAQ,EAAE,GAAG,OAAO,CAAC;QAC9C,SAAS;QACT,QAAQ;QACR,SAAS,CAAC,KAAe,CAAC,QAAQ,EAAE,GAAG,QAAQ,CAAC;QAChD,SAAS;YACP,OAAO;YACP,OAAO;QACT;QACA,QAAQ;IACV;IAEA,QAAQ;IACR,OAAO;QACL,MAAM;QACN,MAAM,CAAC,KAAe,CAAC,OAAO,EAAE,IAAI;QACpC,SAAS,CAAC,QAAkB,CAAC,aAAa,EAAE,OAAO;QACnD,QAAQ,CAAC,OAAiB,CAAC,YAAY,EAAE,MAAM;QAC/C,QAAQ;QACR,QAAQ;QACR,YAAY,CAAC,KAAe,CAAC,OAAO,EAAE,GAAG,WAAW,CAAC;QACrD,UAAU,CAAC,KAAe,CAAC,OAAO,EAAE,GAAG,SAAS,CAAC;QACjD,YAAY,CAAC,KAAe,CAAC,OAAO,EAAE,GAAG,OAAO,CAAC;QACjD,aAAa,CAAC,QAAkB,CAAC,oBAAoB,EAAE,OAAO;QAC9D,QAAQ;IACV;IAEA,UAAU;IACV,MAAM;QACJ,KAAK;QACL,UAAU;QACV,WAAW;QACX,aAAa;QACb,UAAU;IACZ;AACF;AAQO,MAAM,iBAAiB;;;IAC5B,YACE,OAAe,EACf,AAAO,MAAe,EACtB,AAAO,MAAiB,CACxB;QACA,KAAK,CAAC,eAHC,SAAA,aACA,SAAA;QAGP,IAAI,CAAC,IAAI,GAAG;IACd;AACF;AAGO,MAAM,mBAAmB,CAAC;IAC/B,MAAM,eAAe,IAAI;IAEzB,OAAO,OAAO,CAAC,QAAQ,OAAO,CAAC,CAAC,CAAC,KAAK,MAAM;QAC1C,IAAI,UAAU,aAAa,UAAU,QAAQ,UAAU,IAAI;YACzD,IAAI,MAAM,OAAO,CAAC,QAAQ;gBACxB,MAAM,OAAO,CAAC,CAAA,OAAQ,aAAa,MAAM,CAAC,KAAK,OAAO;YACxD,OAAO;gBACL,aAAa,MAAM,CAAC,KAAK,OAAO;YAClC;QACF;IACF;IAEA,MAAM,cAAc,aAAa,QAAQ;IACzC,OAAO,cAAc,CAAC,CAAC,EAAE,aAAa,GAAG;AAC3C;AAGO,MAAM,iBAAiB;IAC5B,gBAAgB;IAChB,UAAU;AACZ", "debugId": null}}, {"offset": {"line": 128, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/cast-stonev2/cast-stonev2/Frontend/cast-stone-frontend/src/services/config/httpClient.ts"], "sourcesContent": ["import { \n  BaseApiUrl, \n  ApiResponse, \n  HttpMethod, \n  RequestConfig, \n  ApiError, \n  defaultHeaders,\n  buildQueryString \n} from './apiConfig';\n\nclass HttpClient {\n  private baseUrl: string;\n\n  constructor(baseUrl: string = BaseApiUrl) {\n    this.baseUrl = baseUrl;\n  }\n\n  private async request<T>(\n    endpoint: string,\n    method: HttpMethod = HttpMethod.GET,\n    config: RequestConfig = {}\n  ): Promise<ApiResponse<T>> {\n    const { params, ...requestConfig } = config;\n    \n    // Build URL with query parameters for GET requests\n    let url = `${this.baseUrl}${endpoint}`;\n    if (method === HttpMethod.GET && params) {\n      url += buildQueryString(params);\n    }\n\n    const requestOptions: RequestInit = {\n      method,\n      headers: {\n        ...defaultHeaders,\n        ...requestConfig.headers,\n      },\n      ...requestConfig,\n    };\n\n    // Add body for non-GET requests\n    if (method !== HttpMethod.GET && requestConfig.body) {\n      if (typeof requestConfig.body === 'object') {\n        requestOptions.body = JSON.stringify(requestConfig.body);\n      }\n    }\n\n    try {\n      const response = await fetch(url, requestOptions);\n      \n      // Handle different response types\n      let responseData: ApiResponse<T>;\n      \n      const contentType = response.headers.get('content-type');\n      if (contentType && contentType.includes('application/json')) {\n        responseData = await response.json();\n      } else {\n        // Handle non-JSON responses\n        const text = await response.text();\n        responseData = {\n          success: response.ok,\n          message: response.ok ? 'Success' : 'Error',\n          data: text as any,\n        };\n      }\n\n      if (!response.ok) {\n        throw new ApiError(\n          responseData.message || `HTTP error! status: ${response.status}`,\n          response.status,\n          responseData.errors\n        );\n      }\n\n      return responseData;\n    } catch (error) {\n      if (error instanceof ApiError) {\n        throw error;\n      }\n      \n      console.error('API request failed:', error);\n      throw new ApiError(\n        error instanceof Error ? error.message : 'Unknown error occurred'\n      );\n    }\n  }\n\n  // GET request\n  async get<T>(endpoint: string, params?: Record<string, any>): Promise<ApiResponse<T>> {\n    return this.request<T>(endpoint, HttpMethod.GET, { params });\n  }\n\n  // POST request\n  async post<T>(endpoint: string, data?: any, config?: RequestConfig): Promise<ApiResponse<T>> {\n    return this.request<T>(endpoint, HttpMethod.POST, {\n      ...config,\n      body: data,\n    });\n  }\n\n  // PUT request\n  async put<T>(endpoint: string, data?: any, config?: RequestConfig): Promise<ApiResponse<T>> {\n    return this.request<T>(endpoint, HttpMethod.PUT, {\n      ...config,\n      body: data,\n    });\n  }\n\n  // PATCH request\n  async patch<T>(endpoint: string, data?: any, config?: RequestConfig): Promise<ApiResponse<T>> {\n    return this.request<T>(endpoint, HttpMethod.PATCH, {\n      ...config,\n      body: data,\n    });\n  }\n\n  // DELETE request\n  async delete<T>(endpoint: string, config?: RequestConfig): Promise<ApiResponse<T>> {\n    return this.request<T>(endpoint, HttpMethod.DELETE, config);\n  }\n\n  // Upload file (multipart/form-data)\n  async upload<T>(endpoint: string, formData: FormData, config?: RequestConfig): Promise<ApiResponse<T>> {\n    const uploadConfig = {\n      ...config,\n      headers: {\n        // Don't set Content-Type for FormData, let browser set it with boundary\n        ...config?.headers,\n      },\n      body: formData,\n    };\n    \n    // Remove Content-Type header for file uploads\n    if (uploadConfig.headers && 'Content-Type' in uploadConfig.headers) {\n      delete uploadConfig.headers['Content-Type'];\n    }\n\n    return this.request<T>(endpoint, HttpMethod.POST, uploadConfig);\n  }\n}\n\n// Export singleton instance\nexport const httpClient = new HttpClient();\n\n// Export class for custom instances if needed\nexport { HttpClient };\n"], "names": [], "mappings": ";;;;AAAA;;AAUA,MAAM;IACI,QAAgB;IAExB,YAAY,UAAkB,yIAAA,CAAA,aAAU,CAAE;QACxC,IAAI,CAAC,OAAO,GAAG;IACjB;IAEA,MAAc,QACZ,QAAgB,EAChB,SAAqB,yIAAA,CAAA,aAAU,CAAC,GAAG,EACnC,SAAwB,CAAC,CAAC,EACD;QACzB,MAAM,EAAE,MAAM,EAAE,GAAG,eAAe,GAAG;QAErC,mDAAmD;QACnD,IAAI,MAAM,GAAG,IAAI,CAAC,OAAO,GAAG,UAAU;QACtC,IAAI,WAAW,yIAAA,CAAA,aAAU,CAAC,GAAG,IAAI,QAAQ;YACvC,OAAO,CAAA,GAAA,yIAAA,CAAA,mBAAgB,AAAD,EAAE;QAC1B;QAEA,MAAM,iBAA8B;YAClC;YACA,SAAS;gBACP,GAAG,yIAAA,CAAA,iBAAc;gBACjB,GAAG,cAAc,OAAO;YAC1B;YACA,GAAG,aAAa;QAClB;QAEA,gCAAgC;QAChC,IAAI,WAAW,yIAAA,CAAA,aAAU,CAAC,GAAG,IAAI,cAAc,IAAI,EAAE;YACnD,IAAI,OAAO,cAAc,IAAI,KAAK,UAAU;gBAC1C,eAAe,IAAI,GAAG,KAAK,SAAS,CAAC,cAAc,IAAI;YACzD;QACF;QAEA,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,KAAK;YAElC,kCAAkC;YAClC,IAAI;YAEJ,MAAM,cAAc,SAAS,OAAO,CAAC,GAAG,CAAC;YACzC,IAAI,eAAe,YAAY,QAAQ,CAAC,qBAAqB;gBAC3D,eAAe,MAAM,SAAS,IAAI;YACpC,OAAO;gBACL,4BAA4B;gBAC5B,MAAM,OAAO,MAAM,SAAS,IAAI;gBAChC,eAAe;oBACb,SAAS,SAAS,EAAE;oBACpB,SAAS,SAAS,EAAE,GAAG,YAAY;oBACnC,MAAM;gBACR;YACF;YAEA,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,yIAAA,CAAA,WAAQ,CAChB,aAAa,OAAO,IAAI,CAAC,oBAAoB,EAAE,SAAS,MAAM,EAAE,EAChE,SAAS,MAAM,EACf,aAAa,MAAM;YAEvB;YAEA,OAAO;QACT,EAAE,OAAO,OAAO;YACd,IAAI,iBAAiB,yIAAA,CAAA,WAAQ,EAAE;gBAC7B,MAAM;YACR;YAEA,QAAQ,KAAK,CAAC,uBAAuB;YACrC,MAAM,IAAI,yIAAA,CAAA,WAAQ,CAChB,iBAAiB,QAAQ,MAAM,OAAO,GAAG;QAE7C;IACF;IAEA,cAAc;IACd,MAAM,IAAO,QAAgB,EAAE,MAA4B,EAA2B;QACpF,OAAO,IAAI,CAAC,OAAO,CAAI,UAAU,yIAAA,CAAA,aAAU,CAAC,GAAG,EAAE;YAAE;QAAO;IAC5D;IAEA,eAAe;IACf,MAAM,KAAQ,QAAgB,EAAE,IAAU,EAAE,MAAsB,EAA2B;QAC3F,OAAO,IAAI,CAAC,OAAO,CAAI,UAAU,yIAAA,CAAA,aAAU,CAAC,IAAI,EAAE;YAChD,GAAG,MAAM;YACT,MAAM;QACR;IACF;IAEA,cAAc;IACd,MAAM,IAAO,QAAgB,EAAE,IAAU,EAAE,MAAsB,EAA2B;QAC1F,OAAO,IAAI,CAAC,OAAO,CAAI,UAAU,yIAAA,CAAA,aAAU,CAAC,GAAG,EAAE;YAC/C,GAAG,MAAM;YACT,MAAM;QACR;IACF;IAEA,gBAAgB;IAChB,MAAM,MAAS,QAAgB,EAAE,IAAU,EAAE,MAAsB,EAA2B;QAC5F,OAAO,IAAI,CAAC,OAAO,CAAI,UAAU,yIAAA,CAAA,aAAU,CAAC,KAAK,EAAE;YACjD,GAAG,MAAM;YACT,MAAM;QACR;IACF;IAEA,iBAAiB;IACjB,MAAM,OAAU,QAAgB,EAAE,MAAsB,EAA2B;QACjF,OAAO,IAAI,CAAC,OAAO,CAAI,UAAU,yIAAA,CAAA,aAAU,CAAC,MAAM,EAAE;IACtD;IAEA,oCAAoC;IACpC,MAAM,OAAU,QAAgB,EAAE,QAAkB,EAAE,MAAsB,EAA2B;QACrG,MAAM,eAAe;YACnB,GAAG,MAAM;YACT,SAAS;gBACP,wEAAwE;gBACxE,GAAG,QAAQ,OAAO;YACpB;YACA,MAAM;QACR;QAEA,8CAA8C;QAC9C,IAAI,aAAa,OAAO,IAAI,kBAAkB,aAAa,OAAO,EAAE;YAClE,OAAO,aAAa,OAAO,CAAC,eAAe;QAC7C;QAEA,OAAO,IAAI,CAAC,OAAO,CAAI,UAAU,yIAAA,CAAA,aAAU,CAAC,IAAI,EAAE;IACpD;AACF;AAGO,MAAM,aAAa,IAAI", "debugId": null}}, {"offset": {"line": 247, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/cast-stonev2/cast-stonev2/Frontend/cast-stone-frontend/src/services/config/baseService.ts"], "sourcesContent": ["import { httpClient } from './httpClient';\nimport { ApiResponse, PaginatedResponse } from './apiConfig';\n\nexport abstract class BaseService {\n  protected client = httpClient;\n\n  /**\n   * Handle API response and extract data\n   */\n  protected async handleResponse<T>(\n    apiCall: Promise<ApiResponse<T>>\n  ): Promise<T> {\n    try {\n      const response = await apiCall;\n      if (response.success && response.data !== undefined) {\n        return response.data;\n      }\n      throw new Error(response.message || 'API call failed');\n    } catch (error) {\n      console.error('API Error:', error);\n      throw error;\n    }\n  }\n\n  /**\n   * Handle paginated API response\n   */\n  protected async handlePaginatedResponse<T>(\n    apiCall: Promise<ApiResponse<PaginatedResponse<T>>>\n  ): Promise<PaginatedResponse<T>> {\n    try {\n      const response = await apiCall;\n      if (response.success && response.data !== undefined) {\n        return response.data;\n      }\n      throw new Error(response.message || 'API call failed');\n    } catch (error) {\n      console.error('API Error:', error);\n      throw error;\n    }\n  }\n\n  /**\n   * Handle API response without data extraction (for operations like delete)\n   */\n  protected async handleVoidResponse(\n    apiCall: Promise<ApiResponse<any>>\n  ): Promise<boolean> {\n    try {\n      const response = await apiCall;\n      return response.success;\n    } catch (error) {\n      console.error('API Error:', error);\n      throw error;\n    }\n  }\n\n  /**\n   * Log API calls in development\n   */\n  protected logApiCall(method: string, endpoint: string, data?: any): void {\n    if (process.env.NODE_ENV === 'development') {\n      console.log(`🌐 API ${method}:`, endpoint, data ? { data } : '');\n    }\n  }\n}\n\n// Utility functions for common operations\nexport class ServiceUtils {\n  /**\n   * Format date for API calls\n   */\n  static formatDate(date: Date): string {\n    return date.toISOString();\n  }\n\n  /**\n   * Parse API date string to Date object\n   */\n  static parseDate(dateString: string): Date {\n    return new Date(dateString);\n  }\n\n  /**\n   * Validate email format\n   */\n  static isValidEmail(email: string): boolean {\n    const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;\n    return emailRegex.test(email);\n  }\n\n  /**\n   * Format currency\n   */\n  static formatCurrency(amount: number, currency: string = 'USD'): string {\n    return new Intl.NumberFormat('en-US', {\n      style: 'currency',\n      currency: currency,\n    }).format(amount);\n  }\n\n  /**\n   * Debounce function for search inputs\n   */\n  static debounce<T extends (...args: any[]) => any>(\n    func: T,\n    wait: number\n  ): (...args: Parameters<T>) => void {\n    let timeout: NodeJS.Timeout;\n    return (...args: Parameters<T>) => {\n      clearTimeout(timeout);\n      timeout = setTimeout(() => func(...args), wait);\n    };\n  }\n\n  /**\n   * Clean undefined values from objects (useful for API params)\n   */\n  static cleanObject<T extends Record<string, any>>(obj: T): Partial<T> {\n    const cleaned: Partial<T> = {};\n    Object.entries(obj).forEach(([key, value]) => {\n      if (value !== undefined && value !== null && value !== '') {\n        cleaned[key as keyof T] = value;\n      }\n    });\n    return cleaned;\n  }\n}\n"], "names": [], "mappings": ";;;;AA6DQ;AA7DR;;AAGO,MAAe;IACV,SAAS,0IAAA,CAAA,aAAU,CAAC;IAE9B;;GAEC,GACD,MAAgB,eACd,OAAgC,EACpB;QACZ,IAAI;YACF,MAAM,WAAW,MAAM;YACvB,IAAI,SAAS,OAAO,IAAI,SAAS,IAAI,KAAK,WAAW;gBACnD,OAAO,SAAS,IAAI;YACtB;YACA,MAAM,IAAI,MAAM,SAAS,OAAO,IAAI;QACtC,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,cAAc;YAC5B,MAAM;QACR;IACF;IAEA;;GAEC,GACD,MAAgB,wBACd,OAAmD,EACpB;QAC/B,IAAI;YACF,MAAM,WAAW,MAAM;YACvB,IAAI,SAAS,OAAO,IAAI,SAAS,IAAI,KAAK,WAAW;gBACnD,OAAO,SAAS,IAAI;YACtB;YACA,MAAM,IAAI,MAAM,SAAS,OAAO,IAAI;QACtC,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,cAAc;YAC5B,MAAM;QACR;IACF;IAEA;;GAEC,GACD,MAAgB,mBACd,OAAkC,EAChB;QAClB,IAAI;YACF,MAAM,WAAW,MAAM;YACvB,OAAO,SAAS,OAAO;QACzB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,cAAc;YAC5B,MAAM;QACR;IACF;IAEA;;GAEC,GACD,AAAU,WAAW,MAAc,EAAE,QAAgB,EAAE,IAAU,EAAQ;QACvE,wCAA4C;YAC1C,QAAQ,GAAG,CAAC,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC,EAAE,UAAU,OAAO;gBAAE;YAAK,IAAI;QAC/D;IACF;AACF;AAGO,MAAM;IACX;;GAEC,GACD,OAAO,WAAW,IAAU,EAAU;QACpC,OAAO,KAAK,WAAW;IACzB;IAEA;;GAEC,GACD,OAAO,UAAU,UAAkB,EAAQ;QACzC,OAAO,IAAI,KAAK;IAClB;IAEA;;GAEC,GACD,OAAO,aAAa,KAAa,EAAW;QAC1C,MAAM,aAAa;QACnB,OAAO,WAAW,IAAI,CAAC;IACzB;IAEA;;GAEC,GACD,OAAO,eAAe,MAAc,EAAE,WAAmB,KAAK,EAAU;QACtE,OAAO,IAAI,KAAK,YAAY,CAAC,SAAS;YACpC,OAAO;YACP,UAAU;QACZ,GAAG,MAAM,CAAC;IACZ;IAEA;;GAEC,GACD,OAAO,SACL,IAAO,EACP,IAAY,EACsB;QAClC,IAAI;QACJ,OAAO,CAAC,GAAG;YACT,aAAa;YACb,UAAU,WAAW,IAAM,QAAQ,OAAO;QAC5C;IACF;IAEA;;GAEC,GACD,OAAO,YAA2C,GAAM,EAAc;QACpE,MAAM,UAAsB,CAAC;QAC7B,OAAO,OAAO,CAAC,KAAK,OAAO,CAAC,CAAC,CAAC,KAAK,MAAM;YACvC,IAAI,UAAU,aAAa,UAAU,QAAQ,UAAU,IAAI;gBACzD,OAAO,CAAC,IAAe,GAAG;YAC5B;QACF;QACA,OAAO;IACT;AACF", "debugId": null}}, {"offset": {"line": 360, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/cast-stonev2/cast-stonev2/Frontend/cast-stone-frontend/src/services/api/collections/get.ts"], "sourcesContent": ["import { BaseService, ServiceUtils } from '../../config/baseService';\nimport { ApiEndpoints } from '../../config/apiConfig';\nimport { \n  Collection, \n  CollectionHierarchy, \n  CollectionFilterRequest,\n  PaginatedResponse \n} from '../../types/entities';\n\nexport class CollectionGetService extends BaseService {\n  /**\n   * Get all collections\n   */\n  async getAll(): Promise<Collection[]> {\n    this.logApiCall('GET', ApiEndpoints.Collections.Base);\n    return this.handleResponse(\n      this.client.get<Collection[]>(ApiEndpoints.Collections.Base)\n    );\n  }\n\n  /**\n   * Get collection by ID\n   */\n  async getById(id: number): Promise<Collection> {\n    this.logApiCall('GET', ApiEndpoints.Collections.ById(id));\n    return this.handleResponse(\n      this.client.get<Collection>(ApiEndpoints.Collections.ById(id))\n    );\n  }\n\n  /**\n   * Get collections by level\n   */\n  async getByLevel(level: number): Promise<Collection[]> {\n    this.logApiCall('GET', ApiEndpoints.Collections.ByLevel(level));\n    return this.handleResponse(\n      this.client.get<Collection[]>(ApiEndpoints.Collections.ByLevel(level))\n    );\n  }\n\n  /**\n   * Get children of a collection\n   */\n  async getChildren(parentId: number): Promise<Collection[]> {\n    this.logApiCall('GET', ApiEndpoints.Collections.Children(parentId));\n    return this.handleResponse(\n      this.client.get<Collection[]>(ApiEndpoints.Collections.Children(parentId))\n    );\n  }\n\n  /**\n   * Get collection hierarchy\n   */\n  async getHierarchy(): Promise<CollectionHierarchy[]> {\n    this.logApiCall('GET', ApiEndpoints.Collections.Hierarchy);\n    return this.handleResponse(\n      this.client.get<CollectionHierarchy[]>(ApiEndpoints.Collections.Hierarchy)\n    );\n  }\n\n  /**\n   * Get published collections\n   */\n  async getPublished(): Promise<Collection[]> {\n    this.logApiCall('GET', ApiEndpoints.Collections.Published);\n    return this.handleResponse(\n      this.client.get<Collection[]>(ApiEndpoints.Collections.Published)\n    );\n  }\n\n  /**\n   * Search collections by name\n   */\n  async search(name: string): Promise<Collection[]> {\n    this.logApiCall('GET', ApiEndpoints.Collections.Search, { name });\n    return this.handleResponse(\n      this.client.get<Collection[]>(ApiEndpoints.Collections.Search, { name })\n    );\n  }\n\n  /**\n   * Get collections with advanced filtering and pagination\n   */\n  async getFiltered(filters: CollectionFilterRequest): Promise<PaginatedResponse<Collection>> {\n    const cleanFilters = ServiceUtils.cleanObject(filters);\n    this.logApiCall('GET', ApiEndpoints.Collections.Filter, cleanFilters);\n    \n    return this.handlePaginatedResponse(\n      this.client.get<PaginatedResponse<Collection>>(\n        ApiEndpoints.Collections.Filter, \n        cleanFilters\n      )\n    );\n  }\n\n  /**\n   * Get collections with default pagination\n   */\n  async getPaginated(\n    pageNumber: number = 1, \n    pageSize: number = 10,\n    sortBy: string = 'createdAt',\n    sortDirection: 'asc' | 'desc' = 'desc'\n  ): Promise<PaginatedResponse<Collection>> {\n    const filters: CollectionFilterRequest = {\n      pageNumber,\n      pageSize,\n      sortBy,\n      sortDirection\n    };\n    \n    return this.getFiltered(filters);\n  }\n\n  /**\n   * Get root collections (level 1)\n   */\n  async getRootCollections(): Promise<Collection[]> {\n    return this.getByLevel(1);\n  }\n\n  /**\n   * Get collections by tag\n   */\n  async getByTag(tag: string): Promise<Collection[]> {\n    const filters: CollectionFilterRequest = {\n      tag,\n      pageSize: 100 // Get all matching collections\n    };\n    \n    const result = await this.getFiltered(filters);\n    return result.data;\n  }\n\n  /**\n   * Get collections created by specific user\n   */\n  async getByCreatedBy(createdBy: string): Promise<Collection[]> {\n    const filters: CollectionFilterRequest = {\n      createdBy,\n      pageSize: 100\n    };\n    \n    const result = await this.getFiltered(filters);\n    return result.data;\n  }\n\n  /**\n   * Get collections created within date range\n   */\n  async getByDateRange(startDate: Date, endDate: Date): Promise<Collection[]> {\n    const filters: CollectionFilterRequest = {\n      createdAfter: ServiceUtils.formatDate(startDate),\n      createdBefore: ServiceUtils.formatDate(endDate),\n      pageSize: 100\n    };\n    \n    const result = await this.getFiltered(filters);\n    return result.data;\n  }\n}\n\n// Export singleton instance\nexport const collectionGetService = new CollectionGetService();\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;AAQO,MAAM,6BAA6B,2IAAA,CAAA,cAAW;IACnD;;GAEC,GACD,MAAM,SAAgC;QACpC,IAAI,CAAC,UAAU,CAAC,OAAO,yIAAA,CAAA,eAAY,CAAC,WAAW,CAAC,IAAI;QACpD,OAAO,IAAI,CAAC,cAAc,CACxB,IAAI,CAAC,MAAM,CAAC,GAAG,CAAe,yIAAA,CAAA,eAAY,CAAC,WAAW,CAAC,IAAI;IAE/D;IAEA;;GAEC,GACD,MAAM,QAAQ,EAAU,EAAuB;QAC7C,IAAI,CAAC,UAAU,CAAC,OAAO,yIAAA,CAAA,eAAY,CAAC,WAAW,CAAC,IAAI,CAAC;QACrD,OAAO,IAAI,CAAC,cAAc,CACxB,IAAI,CAAC,MAAM,CAAC,GAAG,CAAa,yIAAA,CAAA,eAAY,CAAC,WAAW,CAAC,IAAI,CAAC;IAE9D;IAEA;;GAEC,GACD,MAAM,WAAW,KAAa,EAAyB;QACrD,IAAI,CAAC,UAAU,CAAC,OAAO,yIAAA,CAAA,eAAY,CAAC,WAAW,CAAC,OAAO,CAAC;QACxD,OAAO,IAAI,CAAC,cAAc,CACxB,IAAI,CAAC,MAAM,CAAC,GAAG,CAAe,yIAAA,CAAA,eAAY,CAAC,WAAW,CAAC,OAAO,CAAC;IAEnE;IAEA;;GAEC,GACD,MAAM,YAAY,QAAgB,EAAyB;QACzD,IAAI,CAAC,UAAU,CAAC,OAAO,yIAAA,CAAA,eAAY,CAAC,WAAW,CAAC,QAAQ,CAAC;QACzD,OAAO,IAAI,CAAC,cAAc,CACxB,IAAI,CAAC,MAAM,CAAC,GAAG,CAAe,yIAAA,CAAA,eAAY,CAAC,WAAW,CAAC,QAAQ,CAAC;IAEpE;IAEA;;GAEC,GACD,MAAM,eAA+C;QACnD,IAAI,CAAC,UAAU,CAAC,OAAO,yIAAA,CAAA,eAAY,CAAC,WAAW,CAAC,SAAS;QACzD,OAAO,IAAI,CAAC,cAAc,CACxB,IAAI,CAAC,MAAM,CAAC,GAAG,CAAwB,yIAAA,CAAA,eAAY,CAAC,WAAW,CAAC,SAAS;IAE7E;IAEA;;GAEC,GACD,MAAM,eAAsC;QAC1C,IAAI,CAAC,UAAU,CAAC,OAAO,yIAAA,CAAA,eAAY,CAAC,WAAW,CAAC,SAAS;QACzD,OAAO,IAAI,CAAC,cAAc,CACxB,IAAI,CAAC,MAAM,CAAC,GAAG,CAAe,yIAAA,CAAA,eAAY,CAAC,WAAW,CAAC,SAAS;IAEpE;IAEA;;GAEC,GACD,MAAM,OAAO,IAAY,EAAyB;QAChD,IAAI,CAAC,UAAU,CAAC,OAAO,yIAAA,CAAA,eAAY,CAAC,WAAW,CAAC,MAAM,EAAE;YAAE;QAAK;QAC/D,OAAO,IAAI,CAAC,cAAc,CACxB,IAAI,CAAC,MAAM,CAAC,GAAG,CAAe,yIAAA,CAAA,eAAY,CAAC,WAAW,CAAC,MAAM,EAAE;YAAE;QAAK;IAE1E;IAEA;;GAEC,GACD,MAAM,YAAY,OAAgC,EAA0C;QAC1F,MAAM,eAAe,2IAAA,CAAA,eAAY,CAAC,WAAW,CAAC;QAC9C,IAAI,CAAC,UAAU,CAAC,OAAO,yIAAA,CAAA,eAAY,CAAC,WAAW,CAAC,MAAM,EAAE;QAExD,OAAO,IAAI,CAAC,uBAAuB,CACjC,IAAI,CAAC,MAAM,CAAC,GAAG,CACb,yIAAA,CAAA,eAAY,CAAC,WAAW,CAAC,MAAM,EAC/B;IAGN;IAEA;;GAEC,GACD,MAAM,aACJ,aAAqB,CAAC,EACtB,WAAmB,EAAE,EACrB,SAAiB,WAAW,EAC5B,gBAAgC,MAAM,EACE;QACxC,MAAM,UAAmC;YACvC;YACA;YACA;YACA;QACF;QAEA,OAAO,IAAI,CAAC,WAAW,CAAC;IAC1B;IAEA;;GAEC,GACD,MAAM,qBAA4C;QAChD,OAAO,IAAI,CAAC,UAAU,CAAC;IACzB;IAEA;;GAEC,GACD,MAAM,SAAS,GAAW,EAAyB;QACjD,MAAM,UAAmC;YACvC;YACA,UAAU,IAAI,+BAA+B;QAC/C;QAEA,MAAM,SAAS,MAAM,IAAI,CAAC,WAAW,CAAC;QACtC,OAAO,OAAO,IAAI;IACpB;IAEA;;GAEC,GACD,MAAM,eAAe,SAAiB,EAAyB;QAC7D,MAAM,UAAmC;YACvC;YACA,UAAU;QACZ;QAEA,MAAM,SAAS,MAAM,IAAI,CAAC,WAAW,CAAC;QACtC,OAAO,OAAO,IAAI;IACpB;IAEA;;GAEC,GACD,MAAM,eAAe,SAAe,EAAE,OAAa,EAAyB;QAC1E,MAAM,UAAmC;YACvC,cAAc,2IAAA,CAAA,eAAY,CAAC,UAAU,CAAC;YACtC,eAAe,2IAAA,CAAA,eAAY,CAAC,UAAU,CAAC;YACvC,UAAU;QACZ;QAEA,MAAM,SAAS,MAAM,IAAI,CAAC,WAAW,CAAC;QACtC,OAAO,OAAO,IAAI;IACpB;AACF;AAGO,MAAM,uBAAuB,IAAI", "debugId": null}}, {"offset": {"line": 480, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/cast-stonev2/cast-stonev2/Frontend/cast-stone-frontend/src/services/api/collections/post.ts"], "sourcesContent": ["import { BaseService } from '../../config/baseService';\nimport { ApiEndpoints } from '../../config/apiConfig';\nimport { Collection, CreateCollectionRequest } from '../../types/entities';\n\nexport class CollectionPostService extends BaseService {\n  /**\n   * Create a new collection\n   */\n  async create(data: CreateCollectionRequest): Promise<Collection> {\n    this.logApiCall('POST', ApiEndpoints.Collections.Base, data);\n    \n    // Validate required fields\n    this.validateCreateRequest(data);\n    \n    return this.handleResponse(\n      this.client.post<Collection>(ApiEndpoints.Collections.Base, data)\n    );\n  }\n\n  /**\n   * Create a root collection (Level 1)\n   */\n  async createRootCollection(\n    name: string,\n    description: string,\n    tags: string[] = [],\n    published: boolean = false,\n    createdBy: string\n  ): Promise<Collection> {\n    const data: CreateCollectionRequest = {\n      name,\n      description,\n      level: 1,\n      parentCollectionId: undefined,\n      childCollectionId: undefined,\n      tags,\n      published,\n      createdBy\n    };\n\n    return this.create(data);\n  }\n\n  /**\n   * Create a sub-collection (Level 2 or 3)\n   */\n  async createSubCollection(\n    name: string,\n    description: string,\n    level: 2 | 3,\n    parentCollectionId: number,\n    tags: string[] = [],\n    published: boolean = false,\n    createdBy: string\n  ): Promise<Collection> {\n    const data: CreateCollectionRequest = {\n      name,\n      description,\n      level,\n      parentCollectionId,\n      childCollectionId: undefined,\n      tags,\n      published,\n      createdBy\n    };\n\n    return this.create(data);\n  }\n\n  /**\n   * Create multiple collections in batch\n   */\n  async createBatch(collections: CreateCollectionRequest[]): Promise<Collection[]> {\n    this.logApiCall('POST', 'Batch Collections', { count: collections.length });\n    \n    const promises = collections.map(collection => this.create(collection));\n    return Promise.all(promises);\n  }\n\n  /**\n   * Refresh parent-child relationships for all collections (maintenance operation)\n   */\n  async refreshAllRelationships(): Promise<{\n    success: boolean;\n    updatedCount: number;\n    message: string;\n  }> {\n    this.logApiCall('POST', ApiEndpoints.Collections.RefreshRelationships);\n\n    try {\n      const response = await this.client.post<number>(ApiEndpoints.Collections.RefreshRelationships);\n\n      if (response.success && response.data !== undefined) {\n        return {\n          success: true,\n          updatedCount: response.data,\n          message: response.message || `Updated ${response.data} collection relationships`\n        };\n      } else {\n        return {\n          success: false,\n          updatedCount: 0,\n          message: response.message || 'Failed to refresh relationships'\n        };\n      }\n    } catch (error) {\n      console.error('Error refreshing collection relationships:', error);\n      return {\n        success: false,\n        updatedCount: 0,\n        message: error instanceof Error ? error.message : 'Unknown error occurred'\n      };\n    }\n  }\n\n  /**\n   * Validate create collection request\n   */\n  private validateCreateRequest(data: CreateCollectionRequest): void {\n    if (!data.name || data.name.trim().length === 0) {\n      throw new Error('Collection name is required');\n    }\n\n    if (data.name.length > 200) {\n      throw new Error('Collection name must be 200 characters or less');\n    }\n\n    if (!data.level || data.level < 1 || data.level > 3) {\n      throw new Error('Collection level must be 1, 2, or 3');\n    }\n\n    if (data.level === 1 && data.parentCollectionId) {\n      throw new Error('Root collections (level 1) cannot have a parent');\n    }\n\n    if (data.level > 1 && !data.parentCollectionId) {\n      throw new Error('Sub-collections (level 2-3) must have a parent');\n    }\n\n    if (!data.createdBy || data.createdBy.trim().length === 0) {\n      throw new Error('CreatedBy is required');\n    }\n\n    if (data.createdBy.length > 100) {\n      throw new Error('CreatedBy must be 100 characters or less');\n    }\n\n    if (data.description && data.description.length > 1000) {\n      throw new Error('Description must be 1000 characters or less');\n    }\n  }\n}\n\n// Export singleton instance\nexport const collectionPostService = new CollectionPostService();\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;AAGO,MAAM,8BAA8B,2IAAA,CAAA,cAAW;IACpD;;GAEC,GACD,MAAM,OAAO,IAA6B,EAAuB;QAC/D,IAAI,CAAC,UAAU,CAAC,QAAQ,yIAAA,CAAA,eAAY,CAAC,WAAW,CAAC,IAAI,EAAE;QAEvD,2BAA2B;QAC3B,IAAI,CAAC,qBAAqB,CAAC;QAE3B,OAAO,IAAI,CAAC,cAAc,CACxB,IAAI,CAAC,MAAM,CAAC,IAAI,CAAa,yIAAA,CAAA,eAAY,CAAC,WAAW,CAAC,IAAI,EAAE;IAEhE;IAEA;;GAEC,GACD,MAAM,qBACJ,IAAY,EACZ,WAAmB,EACnB,OAAiB,EAAE,EACnB,YAAqB,KAAK,EAC1B,SAAiB,EACI;QACrB,MAAM,OAAgC;YACpC;YACA;YACA,OAAO;YACP,oBAAoB;YACpB,mBAAmB;YACnB;YACA;YACA;QACF;QAEA,OAAO,IAAI,CAAC,MAAM,CAAC;IACrB;IAEA;;GAEC,GACD,MAAM,oBACJ,IAAY,EACZ,WAAmB,EACnB,KAAY,EACZ,kBAA0B,EAC1B,OAAiB,EAAE,EACnB,YAAqB,KAAK,EAC1B,SAAiB,EACI;QACrB,MAAM,OAAgC;YACpC;YACA;YACA;YACA;YACA,mBAAmB;YACnB;YACA;YACA;QACF;QAEA,OAAO,IAAI,CAAC,MAAM,CAAC;IACrB;IAEA;;GAEC,GACD,MAAM,YAAY,WAAsC,EAAyB;QAC/E,IAAI,CAAC,UAAU,CAAC,QAAQ,qBAAqB;YAAE,OAAO,YAAY,MAAM;QAAC;QAEzE,MAAM,WAAW,YAAY,GAAG,CAAC,CAAA,aAAc,IAAI,CAAC,MAAM,CAAC;QAC3D,OAAO,QAAQ,GAAG,CAAC;IACrB;IAEA;;GAEC,GACD,MAAM,0BAIH;QACD,IAAI,CAAC,UAAU,CAAC,QAAQ,yIAAA,CAAA,eAAY,CAAC,WAAW,CAAC,oBAAoB;QAErE,IAAI;YACF,MAAM,WAAW,MAAM,IAAI,CAAC,MAAM,CAAC,IAAI,CAAS,yIAAA,CAAA,eAAY,CAAC,WAAW,CAAC,oBAAoB;YAE7F,IAAI,SAAS,OAAO,IAAI,SAAS,IAAI,KAAK,WAAW;gBACnD,OAAO;oBACL,SAAS;oBACT,cAAc,SAAS,IAAI;oBAC3B,SAAS,SAAS,OAAO,IAAI,CAAC,QAAQ,EAAE,SAAS,IAAI,CAAC,yBAAyB,CAAC;gBAClF;YACF,OAAO;gBACL,OAAO;oBACL,SAAS;oBACT,cAAc;oBACd,SAAS,SAAS,OAAO,IAAI;gBAC/B;YACF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,8CAA8C;YAC5D,OAAO;gBACL,SAAS;gBACT,cAAc;gBACd,SAAS,iBAAiB,QAAQ,MAAM,OAAO,GAAG;YACpD;QACF;IACF;IAEA;;GAEC,GACD,AAAQ,sBAAsB,IAA6B,EAAQ;QACjE,IAAI,CAAC,KAAK,IAAI,IAAI,KAAK,IAAI,CAAC,IAAI,GAAG,MAAM,KAAK,GAAG;YAC/C,MAAM,IAAI,MAAM;QAClB;QAEA,IAAI,KAAK,IAAI,CAAC,MAAM,GAAG,KAAK;YAC1B,MAAM,IAAI,MAAM;QAClB;QAEA,IAAI,CAAC,KAAK,KAAK,IAAI,KAAK,KAAK,GAAG,KAAK,KAAK,KAAK,GAAG,GAAG;YACnD,MAAM,IAAI,MAAM;QAClB;QAEA,IAAI,KAAK,KAAK,KAAK,KAAK,KAAK,kBAAkB,EAAE;YAC/C,MAAM,IAAI,MAAM;QAClB;QAEA,IAAI,KAAK,KAAK,GAAG,KAAK,CAAC,KAAK,kBAAkB,EAAE;YAC9C,MAAM,IAAI,MAAM;QAClB;QAEA,IAAI,CAAC,KAAK,SAAS,IAAI,KAAK,SAAS,CAAC,IAAI,GAAG,MAAM,KAAK,GAAG;YACzD,MAAM,IAAI,MAAM;QAClB;QAEA,IAAI,KAAK,SAAS,CAAC,MAAM,GAAG,KAAK;YAC/B,MAAM,IAAI,MAAM;QAClB;QAEA,IAAI,KAAK,WAAW,IAAI,KAAK,WAAW,CAAC,MAAM,GAAG,MAAM;YACtD,MAAM,IAAI,MAAM;QAClB;IACF;AACF;AAGO,MAAM,wBAAwB,IAAI", "debugId": null}}, {"offset": {"line": 603, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/cast-stonev2/cast-stonev2/Frontend/cast-stone-frontend/src/services/api/collections/update.ts"], "sourcesContent": ["import { BaseService } from '../../config/baseService';\nimport { ApiEndpoints } from '../../config/apiConfig';\nimport { Collection, UpdateCollectionRequest } from '../../types/entities';\n\nexport class CollectionUpdateService extends BaseService {\n  /**\n   * Update an existing collection\n   */\n  async update(id: number, data: UpdateCollectionRequest): Promise<Collection> {\n    this.logApiCall('PUT', ApiEndpoints.Collections.ById(id), data);\n    \n    // Validate required fields\n    this.validateUpdateRequest(data);\n    \n    return this.handleResponse(\n      this.client.put<Collection>(ApiEndpoints.Collections.ById(id), data)\n    );\n  }\n\n  /**\n   * Update collection name and description\n   */\n  async updateBasicInfo(\n    id: number,\n    name: string,\n    description: string,\n    updatedBy: string\n  ): Promise<Collection> {\n    // First get the current collection to preserve other fields\n    const currentCollection = await this.client.get<Collection>(\n      ApiEndpoints.Collections.ById(id)\n    );\n\n    if (!currentCollection.success || !currentCollection.data) {\n      throw new Error('Collection not found');\n    }\n\n    const data: UpdateCollectionRequest = {\n      name,\n      description,\n      level: currentCollection.data.level,\n      parentCollectionId: currentCollection.data.parentCollectionId,\n      childCollectionId: currentCollection.data.childCollectionId,\n      tags: currentCollection.data.tags,\n      published: currentCollection.data.published,\n      updatedBy\n    };\n\n    return this.update(id, data);\n  }\n\n  /**\n   * Update collection tags\n   */\n  async updateTags(\n    id: number,\n    tags: string[],\n    updatedBy: string\n  ): Promise<Collection> {\n    const currentCollection = await this.client.get<Collection>(\n      ApiEndpoints.Collections.ById(id)\n    );\n\n    if (!currentCollection.success || !currentCollection.data) {\n      throw new Error('Collection not found');\n    }\n\n    const data: UpdateCollectionRequest = {\n      name: currentCollection.data.name,\n      description: currentCollection.data.description,\n      level: currentCollection.data.level,\n      parentCollectionId: currentCollection.data.parentCollectionId,\n      childCollectionId: currentCollection.data.childCollectionId,\n      tags,\n      published: currentCollection.data.published,\n      updatedBy\n    };\n\n    return this.update(id, data);\n  }\n\n  /**\n   * Publish or unpublish a collection\n   */\n  async updatePublishStatus(\n    id: number,\n    published: boolean,\n    updatedBy: string\n  ): Promise<Collection> {\n    const currentCollection = await this.client.get<Collection>(\n      ApiEndpoints.Collections.ById(id)\n    );\n\n    if (!currentCollection.success || !currentCollection.data) {\n      throw new Error('Collection not found');\n    }\n\n    const data: UpdateCollectionRequest = {\n      name: currentCollection.data.name,\n      description: currentCollection.data.description,\n      level: currentCollection.data.level,\n      parentCollectionId: currentCollection.data.parentCollectionId,\n      childCollectionId: currentCollection.data.childCollectionId,\n      tags: currentCollection.data.tags,\n      published,\n      updatedBy\n    };\n\n    return this.update(id, data);\n  }\n\n  /**\n   * Move collection to different parent (change hierarchy)\n   */\n  async moveToParent(\n    id: number,\n    newParentId: number | undefined,\n    newLevel: number,\n    updatedBy: string\n  ): Promise<Collection> {\n    const currentCollection = await this.client.get<Collection>(\n      ApiEndpoints.Collections.ById(id)\n    );\n\n    if (!currentCollection.success || !currentCollection.data) {\n      throw new Error('Collection not found');\n    }\n\n    const data: UpdateCollectionRequest = {\n      name: currentCollection.data.name,\n      description: currentCollection.data.description,\n      level: newLevel,\n      parentCollectionId: newParentId,\n      childCollectionId: currentCollection.data.childCollectionId,\n      tags: currentCollection.data.tags,\n      published: currentCollection.data.published,\n      updatedBy\n    };\n\n    return this.update(id, data);\n  }\n\n  /**\n   * Add tags to existing collection\n   */\n  async addTags(\n    id: number,\n    newTags: string[],\n    updatedBy: string\n  ): Promise<Collection> {\n    const currentCollection = await this.client.get<Collection>(\n      ApiEndpoints.Collections.ById(id)\n    );\n\n    if (!currentCollection.success || !currentCollection.data) {\n      throw new Error('Collection not found');\n    }\n\n    const existingTags = currentCollection.data.tags || [];\n    const uniqueTags = [...new Set([...existingTags, ...newTags])];\n\n    return this.updateTags(id, uniqueTags, updatedBy);\n  }\n\n  /**\n   * Remove tags from existing collection\n   */\n  async removeTags(\n    id: number,\n    tagsToRemove: string[],\n    updatedBy: string\n  ): Promise<Collection> {\n    const currentCollection = await this.client.get<Collection>(\n      ApiEndpoints.Collections.ById(id)\n    );\n\n    if (!currentCollection.success || !currentCollection.data) {\n      throw new Error('Collection not found');\n    }\n\n    const existingTags = currentCollection.data.tags || [];\n    const filteredTags = existingTags.filter(tag => !tagsToRemove.includes(tag));\n\n    return this.updateTags(id, filteredTags, updatedBy);\n  }\n\n  /**\n   * Validate update collection request\n   */\n  private validateUpdateRequest(data: UpdateCollectionRequest): void {\n    if (!data.name || data.name.trim().length === 0) {\n      throw new Error('Collection name is required');\n    }\n\n    if (data.name.length > 200) {\n      throw new Error('Collection name must be 200 characters or less');\n    }\n\n    if (!data.level || data.level < 1 || data.level > 3) {\n      throw new Error('Collection level must be 1, 2, or 3');\n    }\n\n    if (data.level === 1 && data.parentCollectionId) {\n      throw new Error('Root collections (level 1) cannot have a parent');\n    }\n\n    if (data.level > 1 && !data.parentCollectionId) {\n      throw new Error('Sub-collections (level 2-3) must have a parent');\n    }\n\n    if (!data.updatedBy || data.updatedBy.trim().length === 0) {\n      throw new Error('UpdatedBy is required');\n    }\n\n    if (data.updatedBy.length > 100) {\n      throw new Error('UpdatedBy must be 100 characters or less');\n    }\n\n    if (data.description && data.description.length > 1000) {\n      throw new Error('Description must be 1000 characters or less');\n    }\n  }\n}\n\n// Export singleton instance\nexport const collectionUpdateService = new CollectionUpdateService();\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;AAGO,MAAM,gCAAgC,2IAAA,CAAA,cAAW;IACtD;;GAEC,GACD,MAAM,OAAO,EAAU,EAAE,IAA6B,EAAuB;QAC3E,IAAI,CAAC,UAAU,CAAC,OAAO,yIAAA,CAAA,eAAY,CAAC,WAAW,CAAC,IAAI,CAAC,KAAK;QAE1D,2BAA2B;QAC3B,IAAI,CAAC,qBAAqB,CAAC;QAE3B,OAAO,IAAI,CAAC,cAAc,CACxB,IAAI,CAAC,MAAM,CAAC,GAAG,CAAa,yIAAA,CAAA,eAAY,CAAC,WAAW,CAAC,IAAI,CAAC,KAAK;IAEnE;IAEA;;GAEC,GACD,MAAM,gBACJ,EAAU,EACV,IAAY,EACZ,WAAmB,EACnB,SAAiB,EACI;QACrB,4DAA4D;QAC5D,MAAM,oBAAoB,MAAM,IAAI,CAAC,MAAM,CAAC,GAAG,CAC7C,yIAAA,CAAA,eAAY,CAAC,WAAW,CAAC,IAAI,CAAC;QAGhC,IAAI,CAAC,kBAAkB,OAAO,IAAI,CAAC,kBAAkB,IAAI,EAAE;YACzD,MAAM,IAAI,MAAM;QAClB;QAEA,MAAM,OAAgC;YACpC;YACA;YACA,OAAO,kBAAkB,IAAI,CAAC,KAAK;YACnC,oBAAoB,kBAAkB,IAAI,CAAC,kBAAkB;YAC7D,mBAAmB,kBAAkB,IAAI,CAAC,iBAAiB;YAC3D,MAAM,kBAAkB,IAAI,CAAC,IAAI;YACjC,WAAW,kBAAkB,IAAI,CAAC,SAAS;YAC3C;QACF;QAEA,OAAO,IAAI,CAAC,MAAM,CAAC,IAAI;IACzB;IAEA;;GAEC,GACD,MAAM,WACJ,EAAU,EACV,IAAc,EACd,SAAiB,EACI;QACrB,MAAM,oBAAoB,MAAM,IAAI,CAAC,MAAM,CAAC,GAAG,CAC7C,yIAAA,CAAA,eAAY,CAAC,WAAW,CAAC,IAAI,CAAC;QAGhC,IAAI,CAAC,kBAAkB,OAAO,IAAI,CAAC,kBAAkB,IAAI,EAAE;YACzD,MAAM,IAAI,MAAM;QAClB;QAEA,MAAM,OAAgC;YACpC,MAAM,kBAAkB,IAAI,CAAC,IAAI;YACjC,aAAa,kBAAkB,IAAI,CAAC,WAAW;YAC/C,OAAO,kBAAkB,IAAI,CAAC,KAAK;YACnC,oBAAoB,kBAAkB,IAAI,CAAC,kBAAkB;YAC7D,mBAAmB,kBAAkB,IAAI,CAAC,iBAAiB;YAC3D;YACA,WAAW,kBAAkB,IAAI,CAAC,SAAS;YAC3C;QACF;QAEA,OAAO,IAAI,CAAC,MAAM,CAAC,IAAI;IACzB;IAEA;;GAEC,GACD,MAAM,oBACJ,EAAU,EACV,SAAkB,EAClB,SAAiB,EACI;QACrB,MAAM,oBAAoB,MAAM,IAAI,CAAC,MAAM,CAAC,GAAG,CAC7C,yIAAA,CAAA,eAAY,CAAC,WAAW,CAAC,IAAI,CAAC;QAGhC,IAAI,CAAC,kBAAkB,OAAO,IAAI,CAAC,kBAAkB,IAAI,EAAE;YACzD,MAAM,IAAI,MAAM;QAClB;QAEA,MAAM,OAAgC;YACpC,MAAM,kBAAkB,IAAI,CAAC,IAAI;YACjC,aAAa,kBAAkB,IAAI,CAAC,WAAW;YAC/C,OAAO,kBAAkB,IAAI,CAAC,KAAK;YACnC,oBAAoB,kBAAkB,IAAI,CAAC,kBAAkB;YAC7D,mBAAmB,kBAAkB,IAAI,CAAC,iBAAiB;YAC3D,MAAM,kBAAkB,IAAI,CAAC,IAAI;YACjC;YACA;QACF;QAEA,OAAO,IAAI,CAAC,MAAM,CAAC,IAAI;IACzB;IAEA;;GAEC,GACD,MAAM,aACJ,EAAU,EACV,WAA+B,EAC/B,QAAgB,EAChB,SAAiB,EACI;QACrB,MAAM,oBAAoB,MAAM,IAAI,CAAC,MAAM,CAAC,GAAG,CAC7C,yIAAA,CAAA,eAAY,CAAC,WAAW,CAAC,IAAI,CAAC;QAGhC,IAAI,CAAC,kBAAkB,OAAO,IAAI,CAAC,kBAAkB,IAAI,EAAE;YACzD,MAAM,IAAI,MAAM;QAClB;QAEA,MAAM,OAAgC;YACpC,MAAM,kBAAkB,IAAI,CAAC,IAAI;YACjC,aAAa,kBAAkB,IAAI,CAAC,WAAW;YAC/C,OAAO;YACP,oBAAoB;YACpB,mBAAmB,kBAAkB,IAAI,CAAC,iBAAiB;YAC3D,MAAM,kBAAkB,IAAI,CAAC,IAAI;YACjC,WAAW,kBAAkB,IAAI,CAAC,SAAS;YAC3C;QACF;QAEA,OAAO,IAAI,CAAC,MAAM,CAAC,IAAI;IACzB;IAEA;;GAEC,GACD,MAAM,QACJ,EAAU,EACV,OAAiB,EACjB,SAAiB,EACI;QACrB,MAAM,oBAAoB,MAAM,IAAI,CAAC,MAAM,CAAC,GAAG,CAC7C,yIAAA,CAAA,eAAY,CAAC,WAAW,CAAC,IAAI,CAAC;QAGhC,IAAI,CAAC,kBAAkB,OAAO,IAAI,CAAC,kBAAkB,IAAI,EAAE;YACzD,MAAM,IAAI,MAAM;QAClB;QAEA,MAAM,eAAe,kBAAkB,IAAI,CAAC,IAAI,IAAI,EAAE;QACtD,MAAM,aAAa;eAAI,IAAI,IAAI;mBAAI;mBAAiB;aAAQ;SAAE;QAE9D,OAAO,IAAI,CAAC,UAAU,CAAC,IAAI,YAAY;IACzC;IAEA;;GAEC,GACD,MAAM,WACJ,EAAU,EACV,YAAsB,EACtB,SAAiB,EACI;QACrB,MAAM,oBAAoB,MAAM,IAAI,CAAC,MAAM,CAAC,GAAG,CAC7C,yIAAA,CAAA,eAAY,CAAC,WAAW,CAAC,IAAI,CAAC;QAGhC,IAAI,CAAC,kBAAkB,OAAO,IAAI,CAAC,kBAAkB,IAAI,EAAE;YACzD,MAAM,IAAI,MAAM;QAClB;QAEA,MAAM,eAAe,kBAAkB,IAAI,CAAC,IAAI,IAAI,EAAE;QACtD,MAAM,eAAe,aAAa,MAAM,CAAC,CAAA,MAAO,CAAC,aAAa,QAAQ,CAAC;QAEvE,OAAO,IAAI,CAAC,UAAU,CAAC,IAAI,cAAc;IAC3C;IAEA;;GAEC,GACD,AAAQ,sBAAsB,IAA6B,EAAQ;QACjE,IAAI,CAAC,KAAK,IAAI,IAAI,KAAK,IAAI,CAAC,IAAI,GAAG,MAAM,KAAK,GAAG;YAC/C,MAAM,IAAI,MAAM;QAClB;QAEA,IAAI,KAAK,IAAI,CAAC,MAAM,GAAG,KAAK;YAC1B,MAAM,IAAI,MAAM;QAClB;QAEA,IAAI,CAAC,KAAK,KAAK,IAAI,KAAK,KAAK,GAAG,KAAK,KAAK,KAAK,GAAG,GAAG;YACnD,MAAM,IAAI,MAAM;QAClB;QAEA,IAAI,KAAK,KAAK,KAAK,KAAK,KAAK,kBAAkB,EAAE;YAC/C,MAAM,IAAI,MAAM;QAClB;QAEA,IAAI,KAAK,KAAK,GAAG,KAAK,CAAC,KAAK,kBAAkB,EAAE;YAC9C,MAAM,IAAI,MAAM;QAClB;QAEA,IAAI,CAAC,KAAK,SAAS,IAAI,KAAK,SAAS,CAAC,IAAI,GAAG,MAAM,KAAK,GAAG;YACzD,MAAM,IAAI,MAAM;QAClB;QAEA,IAAI,KAAK,SAAS,CAAC,MAAM,GAAG,KAAK;YAC/B,MAAM,IAAI,MAAM;QAClB;QAEA,IAAI,KAAK,WAAW,IAAI,KAAK,WAAW,CAAC,MAAM,GAAG,MAAM;YACtD,MAAM,IAAI,MAAM;QAClB;IACF;AACF;AAGO,MAAM,0BAA0B,IAAI", "debugId": null}}, {"offset": {"line": 763, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/cast-stonev2/cast-stonev2/Frontend/cast-stone-frontend/src/services/api/collections/delete.ts"], "sourcesContent": ["import { BaseService } from '../../config/baseService';\nimport { ApiEndpoints } from '../../config/apiConfig';\n\nexport class CollectionDeleteService extends BaseService {\n  /**\n   * Delete a collection by ID\n   */\n  async delete(id: number): Promise<boolean> {\n    this.logApiCall('DELETE', ApiEndpoints.Collections.ById(id));\n    \n    return this.handleVoidResponse(\n      this.client.delete(ApiEndpoints.Collections.ById(id))\n    );\n  }\n\n  /**\n   * Delete multiple collections\n   */\n  async deleteBatch(ids: number[]): Promise<boolean[]> {\n    this.logApiCall('DELETE', 'Batch Collections', { count: ids.length });\n    \n    const promises = ids.map(id => this.delete(id));\n    return Promise.all(promises);\n  }\n\n  /**\n   * Soft delete - unpublish collection instead of deleting\n   */\n  async unpublish(id: number, updatedBy: string): Promise<boolean> {\n    this.logApiCall('PATCH', `Unpublish Collection ${id}`);\n    \n    try {\n      // Import here to avoid circular dependency\n      const { collectionUpdateService } = await import('./update');\n      await collectionUpdateService.updatePublishStatus(id, false, updatedBy);\n      return true;\n    } catch (error) {\n      console.error('Failed to unpublish collection:', error);\n      return false;\n    }\n  }\n\n  /**\n   * Check if collection can be safely deleted\n   */\n  async canDelete(id: number): Promise<{\n    canDelete: boolean;\n    reason?: string;\n    hasChildren?: boolean;\n    hasProducts?: boolean;\n  }> {\n    try {\n      // Import here to avoid circular dependency\n      const { collectionGetService } = await import('./get');\n      \n      // Check if collection has children\n      const children = await collectionGetService.getChildren(id);\n      const hasChildren = children.length > 0;\n\n      // Get collection with products to check if it has products\n      const collection = await collectionGetService.getById(id);\n      const hasProducts = collection.products && collection.products.length > 0;\n\n      const canDelete = !hasChildren && !hasProducts;\n      \n      let reason: string | undefined;\n      if (!canDelete) {\n        if (hasChildren && hasProducts) {\n          reason = 'Collection has both child collections and products';\n        } else if (hasChildren) {\n          reason = 'Collection has child collections';\n        } else if (hasProducts) {\n          reason = 'Collection has products';\n        }\n      }\n\n      return {\n        canDelete,\n        reason,\n        hasChildren,\n        hasProducts\n      };\n    } catch (error) {\n      console.error('Error checking if collection can be deleted:', error);\n      return {\n        canDelete: false,\n        reason: 'Error checking collection dependencies'\n      };\n    }\n  }\n\n  /**\n   * Safe delete - checks dependencies before deleting\n   */\n  async safeDelete(id: number): Promise<{\n    success: boolean;\n    message: string;\n  }> {\n    try {\n      const deleteCheck = await this.canDelete(id);\n      \n      if (!deleteCheck.canDelete) {\n        return {\n          success: false,\n          message: deleteCheck.reason || 'Collection cannot be deleted'\n        };\n      }\n\n      const deleted = await this.delete(id);\n      \n      if (deleted) {\n        return {\n          success: true,\n          message: 'Collection deleted successfully'\n        };\n      } else {\n        return {\n          success: false,\n          message: 'Failed to delete collection'\n        };\n      }\n    } catch (error) {\n      console.error('Error during safe delete:', error);\n      return {\n        success: false,\n        message: error instanceof Error ? error.message : 'Unknown error occurred'\n      };\n    }\n  }\n\n  /**\n   * Force delete with cascade (delete children and move products)\n   * Note: This should be used with extreme caution\n   */\n  async forceDelete(\n    id: number, \n    moveProductsToCollectionId?: number\n  ): Promise<{\n    success: boolean;\n    message: string;\n    deletedCollections?: number[];\n    movedProducts?: number;\n  }> {\n    try {\n      const { collectionGetService } = await import('./get');\n      const deletedCollections: number[] = [];\n      let movedProducts = 0;\n\n      // Get collection details\n      const collection = await collectionGetService.getById(id);\n      \n      // Move products to another collection if specified\n      if (moveProductsToCollectionId && collection.products.length > 0) {\n        // This would require product service - placeholder for now\n        movedProducts = collection.products.length;\n        console.warn('Product moving not implemented - would move', movedProducts, 'products');\n      }\n\n      // Delete children recursively\n      const children = await collectionGetService.getChildren(id);\n      for (const child of children) {\n        const childResult = await this.forceDelete(child.id, moveProductsToCollectionId);\n        if (childResult.success && childResult.deletedCollections) {\n          deletedCollections.push(...childResult.deletedCollections);\n        }\n      }\n\n      // Delete the collection itself\n      const deleted = await this.delete(id);\n      if (deleted) {\n        deletedCollections.push(id);\n      }\n\n      return {\n        success: deleted,\n        message: deleted \n          ? `Successfully deleted collection and ${deletedCollections.length - 1} child collections`\n          : 'Failed to delete collection',\n        deletedCollections,\n        movedProducts\n      };\n    } catch (error) {\n      console.error('Error during force delete:', error);\n      return {\n        success: false,\n        message: error instanceof Error ? error.message : 'Unknown error occurred'\n      };\n    }\n  }\n}\n\n// Export singleton instance\nexport const collectionDeleteService = new CollectionDeleteService();\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;AAEO,MAAM,gCAAgC,2IAAA,CAAA,cAAW;IACtD;;GAEC,GACD,MAAM,OAAO,EAAU,EAAoB;QACzC,IAAI,CAAC,UAAU,CAAC,UAAU,yIAAA,CAAA,eAAY,CAAC,WAAW,CAAC,IAAI,CAAC;QAExD,OAAO,IAAI,CAAC,kBAAkB,CAC5B,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,yIAAA,CAAA,eAAY,CAAC,WAAW,CAAC,IAAI,CAAC;IAErD;IAEA;;GAEC,GACD,MAAM,YAAY,GAAa,EAAsB;QACnD,IAAI,CAAC,UAAU,CAAC,UAAU,qBAAqB;YAAE,OAAO,IAAI,MAAM;QAAC;QAEnE,MAAM,WAAW,IAAI,GAAG,CAAC,CAAA,KAAM,IAAI,CAAC,MAAM,CAAC;QAC3C,OAAO,QAAQ,GAAG,CAAC;IACrB;IAEA;;GAEC,GACD,MAAM,UAAU,EAAU,EAAE,SAAiB,EAAoB;QAC/D,IAAI,CAAC,UAAU,CAAC,SAAS,CAAC,qBAAqB,EAAE,IAAI;QAErD,IAAI;YACF,2CAA2C;YAC3C,MAAM,EAAE,uBAAuB,EAAE,GAAG;YACpC,MAAM,wBAAwB,mBAAmB,CAAC,IAAI,OAAO;YAC7D,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,mCAAmC;YACjD,OAAO;QACT;IACF;IAEA;;GAEC,GACD,MAAM,UAAU,EAAU,EAKvB;QACD,IAAI;YACF,2CAA2C;YAC3C,MAAM,EAAE,oBAAoB,EAAE,GAAG;YAEjC,mCAAmC;YACnC,MAAM,WAAW,MAAM,qBAAqB,WAAW,CAAC;YACxD,MAAM,cAAc,SAAS,MAAM,GAAG;YAEtC,2DAA2D;YAC3D,MAAM,aAAa,MAAM,qBAAqB,OAAO,CAAC;YACtD,MAAM,cAAc,WAAW,QAAQ,IAAI,WAAW,QAAQ,CAAC,MAAM,GAAG;YAExE,MAAM,YAAY,CAAC,eAAe,CAAC;YAEnC,IAAI;YACJ,IAAI,CAAC,WAAW;gBACd,IAAI,eAAe,aAAa;oBAC9B,SAAS;gBACX,OAAO,IAAI,aAAa;oBACtB,SAAS;gBACX,OAAO,IAAI,aAAa;oBACtB,SAAS;gBACX;YACF;YAEA,OAAO;gBACL;gBACA;gBACA;gBACA;YACF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,gDAAgD;YAC9D,OAAO;gBACL,WAAW;gBACX,QAAQ;YACV;QACF;IACF;IAEA;;GAEC,GACD,MAAM,WAAW,EAAU,EAGxB;QACD,IAAI;YACF,MAAM,cAAc,MAAM,IAAI,CAAC,SAAS,CAAC;YAEzC,IAAI,CAAC,YAAY,SAAS,EAAE;gBAC1B,OAAO;oBACL,SAAS;oBACT,SAAS,YAAY,MAAM,IAAI;gBACjC;YACF;YAEA,MAAM,UAAU,MAAM,IAAI,CAAC,MAAM,CAAC;YAElC,IAAI,SAAS;gBACX,OAAO;oBACL,SAAS;oBACT,SAAS;gBACX;YACF,OAAO;gBACL,OAAO;oBACL,SAAS;oBACT,SAAS;gBACX;YACF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,6BAA6B;YAC3C,OAAO;gBACL,SAAS;gBACT,SAAS,iBAAiB,QAAQ,MAAM,OAAO,GAAG;YACpD;QACF;IACF;IAEA;;;GAGC,GACD,MAAM,YACJ,EAAU,EACV,0BAAmC,EAMlC;QACD,IAAI;YACF,MAAM,EAAE,oBAAoB,EAAE,GAAG;YACjC,MAAM,qBAA+B,EAAE;YACvC,IAAI,gBAAgB;YAEpB,yBAAyB;YACzB,MAAM,aAAa,MAAM,qBAAqB,OAAO,CAAC;YAEtD,mDAAmD;YACnD,IAAI,8BAA8B,WAAW,QAAQ,CAAC,MAAM,GAAG,GAAG;gBAChE,2DAA2D;gBAC3D,gBAAgB,WAAW,QAAQ,CAAC,MAAM;gBAC1C,QAAQ,IAAI,CAAC,+CAA+C,eAAe;YAC7E;YAEA,8BAA8B;YAC9B,MAAM,WAAW,MAAM,qBAAqB,WAAW,CAAC;YACxD,KAAK,MAAM,SAAS,SAAU;gBAC5B,MAAM,cAAc,MAAM,IAAI,CAAC,WAAW,CAAC,MAAM,EAAE,EAAE;gBACrD,IAAI,YAAY,OAAO,IAAI,YAAY,kBAAkB,EAAE;oBACzD,mBAAmB,IAAI,IAAI,YAAY,kBAAkB;gBAC3D;YACF;YAEA,+BAA+B;YAC/B,MAAM,UAAU,MAAM,IAAI,CAAC,MAAM,CAAC;YAClC,IAAI,SAAS;gBACX,mBAAmB,IAAI,CAAC;YAC1B;YAEA,OAAO;gBACL,SAAS;gBACT,SAAS,UACL,CAAC,oCAAoC,EAAE,mBAAmB,MAAM,GAAG,EAAE,kBAAkB,CAAC,GACxF;gBACJ;gBACA;YACF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,8BAA8B;YAC5C,OAAO;gBACL,SAAS;gBACT,SAAS,iBAAiB,QAAQ,MAAM,OAAO,GAAG;YACpD;QACF;IACF;AACF;AAGO,MAAM,0BAA0B,IAAI", "debugId": null}}, {"offset": {"line": 923, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/cast-stonev2/cast-stonev2/Frontend/cast-stone-frontend/src/services/api/collections/index.ts"], "sourcesContent": ["// Collection Services\nexport { CollectionGetService, collectionGetService } from './get';\nexport { CollectionPostService, collectionPostService } from './post';\nexport { CollectionUpdateService, collectionUpdateService } from './update';\nexport { CollectionDeleteService, collectionDeleteService } from './delete';\n\n// Combined Collection Service\nimport { collectionGetService } from './get';\nimport { collectionPostService } from './post';\nimport { collectionUpdateService } from './update';\nimport { collectionDeleteService } from './delete';\n\nexport class CollectionService {\n  get = collectionGetService;\n  post = collectionPostService;\n  update = collectionUpdateService;\n  delete = collectionDeleteService;\n}\n\n// Export singleton instance\nexport const collectionService = new CollectionService();\n"], "names": [], "mappings": "AAAA,sBAAsB;;;;;AACtB;AACA;AACA;AACA;;;;;;;;;AAQO,MAAM;IACX,MAAM,+IAAA,CAAA,uBAAoB,CAAC;IAC3B,OAAO,gJAAA,CAAA,wBAAqB,CAAC;IAC7B,SAAS,kJAAA,CAAA,0BAAuB,CAAC;IACjC,SAAS,kJAAA,CAAA,0BAAuB,CAAC;AACnC;AAGO,MAAM,oBAAoB,IAAI", "debugId": null}}, {"offset": {"line": 967, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/components/shared/Header/header.module.css [app-client] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"active\": \"header-module__zhIT0W__active\",\n  \"cartContainer\": \"header-module__zhIT0W__cartContainer\",\n  \"cartLink\": \"header-module__zhIT0W__cartLink\",\n  \"container\": \"header-module__zhIT0W__container\",\n  \"dropdown\": \"header-module__zhIT0W__dropdown\",\n  \"dropdownContainer\": \"header-module__zhIT0W__dropdownContainer\",\n  \"dropdownIcon\": \"header-module__zhIT0W__dropdownIcon\",\n  \"dropdownItem\": \"header-module__zhIT0W__dropdownItem\",\n  \"dropdownLink\": \"header-module__zhIT0W__dropdownLink\",\n  \"dropdownList\": \"header-module__zhIT0W__dropdownList\",\n  \"dropdownSlideIn\": \"header-module__zhIT0W__dropdownSlideIn\",\n  \"header\": \"header-module__zhIT0W__header\",\n  \"loadingIcon\": \"header-module__zhIT0W__loadingIcon\",\n  \"logo\": \"header-module__zhIT0W__logo\",\n  \"logoLink\": \"header-module__zhIT0W__logoLink\",\n  \"logoSubtext\": \"header-module__zhIT0W__logoSubtext\",\n  \"logoText\": \"header-module__zhIT0W__logoText\",\n  \"nav\": \"header-module__zhIT0W__nav\",\n  \"navButton\": \"header-module__zhIT0W__navButton\",\n  \"navItem\": \"header-module__zhIT0W__navItem\",\n  \"navLink\": \"header-module__zhIT0W__navLink\",\n  \"navList\": \"header-module__zhIT0W__navList\",\n  \"rotated\": \"header-module__zhIT0W__rotated\",\n  \"subDropdownItem\": \"header-module__zhIT0W__subDropdownItem\",\n  \"subDropdownLink\": \"header-module__zhIT0W__subDropdownLink\",\n  \"subDropdownList\": \"header-module__zhIT0W__subDropdownList\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA"}}, {"offset": {"line": 1001, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/cast-stonev2/cast-stonev2/Frontend/cast-stone-frontend/src/components/shared/Header/Header.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState, useEffect, useRef } from 'react';\nimport Link from 'next/link';\nimport { collectionGetService } from '../../../services/api/collections';\nimport { CollectionHierarchy } from '../../../services/types/entities';\nimport { DropdownItem } from '../../../types';\nimport styles from './header.module.css';\n\ninterface HeaderProps {\n  title?: string;\n}\n\nconst Header: React.FC<HeaderProps> = ({ title = \"Cast Stone\" }) => {\n  const [collections, setCollections] = useState<CollectionHierarchy[]>([]);\n  const [activeDropdown, setActiveDropdown] = useState<string | null>(null);\n  const [isLoading, setIsLoading] = useState(false);\n  const dropdownRefs = useRef<{ [key: string]: HTMLDivElement | null }>({});\n\n  // Company dropdown items\n  const companyItems: DropdownItem[] = [\n    { label: 'Contact Us', href: '/contact' },\n    { label: 'Our Story', href: '/our-story' },\n    { label: 'Retail Locator', href: '/retail-locator' },\n    { label: 'Wholesale Signup', href: '/wholesale-signup' }\n  ];\n\n  // Discover dropdown items\n  const discoverItems: DropdownItem[] = [\n    { label: 'Catalog', href: '/catalog' },\n    { label: 'Finishes', href: '/finishes' },\n    { label: 'Videos', href: '/videos' },\n    { label: 'Technical Info', href: '/technical-info' },\n    { label: 'FAQ', href: '/faq' }\n  ];\n\n  // Fetch collections on component mount\n  useEffect(() => {\n    const fetchCollections = async () => {\n      try {\n        setIsLoading(true);\n        const hierarchyData = await collectionGetService.getHierarchy();\n        setCollections(hierarchyData);\n      } catch (error) {\n        console.error('Failed to fetch collections:', error);\n      } finally {\n        setIsLoading(false);\n      }\n    };\n\n    fetchCollections();\n  }, []);\n\n  // Handle dropdown toggle\n  const handleDropdownToggle = (dropdownName: string) => {\n    setActiveDropdown(activeDropdown === dropdownName ? null : dropdownName);\n  };\n\n  // Handle click outside to close dropdown\n  useEffect(() => {\n    const handleClickOutside = (event: MouseEvent) => {\n      const target = event.target as Node;\n      const isClickInsideDropdown = Object.values(dropdownRefs.current).some(\n        ref => ref && ref.contains(target)\n      );\n\n      if (!isClickInsideDropdown) {\n        setActiveDropdown(null);\n      }\n    };\n\n    document.addEventListener('mousedown', handleClickOutside);\n    return () => document.removeEventListener('mousedown', handleClickOutside);\n  }, []);\n\n  // Convert collections to dropdown items\n  const collectionsToDropdownItems = (collections: CollectionHierarchy[]): DropdownItem[] => {\n    return collections.map(collection => ({\n      label: collection.name,\n      href: `/collections/${collection.id}`,\n      children: collection.children.length > 0\n        ? collectionsToDropdownItems(collection.children)\n        : undefined\n    }));\n  };\n\n  const collectionItems = collectionsToDropdownItems(collections);\n\n  return (\n    <header className={styles.header}>\n      <div className={styles.container}>\n        {/* Logo */}\n        <div className={styles.logo}>\n          <Link href=\"/\" className={styles.logoLink}>\n            <span className={styles.logoText}>{title}</span>\n            <span className={styles.logoSubtext}>Interiors & Decorations</span>\n          </Link>\n        </div>\n\n        {/* Navigation */}\n        <nav className={styles.nav}>\n          <ul className={styles.navList}>\n            {/* Company Dropdown */}\n            <li className={styles.navItem}>\n              <div\n                className={styles.dropdownContainer}\n                ref={el => dropdownRefs.current['company'] = el}\n              >\n                <button\n                  className={`${styles.navButton} ${activeDropdown === 'company' ? styles.active : ''}`}\n                  onClick={() => handleDropdownToggle('company')}\n                  aria-expanded={activeDropdown === 'company'}\n                >\n                  Company\n                  <span className={`${styles.dropdownIcon} ${activeDropdown === 'company' ? styles.rotated : ''}`}>\n                    <svg width=\"12\" height=\"8\" viewBox=\"0 0 12 8\" fill=\"none\">\n                      <path d=\"M1 1.5L6 6.5L11 1.5\" stroke=\"currentColor\" strokeWidth=\"1.5\" strokeLinecap=\"round\" strokeLinejoin=\"round\"/>\n                    </svg>\n                  </span>\n                </button>\n                {activeDropdown === 'company' && (\n                  <div className={styles.dropdown}>\n                    <ul className={styles.dropdownList}>\n                      {companyItems.map((item, index) => (\n                        <li key={index} className={styles.dropdownItem}>\n                          <Link href={item.href} className={styles.dropdownLink}>\n                            {item.label}\n                          </Link>\n                        </li>\n                      ))}\n                    </ul>\n                  </div>\n                )}\n              </div>\n            </li>\n\n            {/* Products */}\n            <li className={styles.navItem}>\n              <Link href=\"/products\" className={styles.navLink}>\n                Products\n              </Link>\n            </li>\n\n            {/* Collections Dropdown */}\n            <li className={styles.navItem}>\n              <div\n                className={styles.dropdownContainer}\n                ref={el => dropdownRefs.current['collections'] = el}\n              >\n                <button\n                  className={`${styles.navButton} ${activeDropdown === 'collections' ? styles.active : ''}`}\n                  onClick={() => handleDropdownToggle('collections')}\n                  aria-expanded={activeDropdown === 'collections'}\n                  disabled={isLoading}\n                >\n                  Collections\n                  {isLoading ? (\n                    <span className={styles.loadingIcon}>\n                      <svg width=\"12\" height=\"12\" viewBox=\"0 0 24 24\" fill=\"none\">\n                        <circle cx=\"12\" cy=\"12\" r=\"10\" stroke=\"currentColor\" strokeWidth=\"2\" strokeLinecap=\"round\" strokeDasharray=\"31.416\" strokeDashoffset=\"31.416\">\n                          <animate attributeName=\"stroke-dasharray\" dur=\"2s\" values=\"0 31.416;15.708 15.708;0 31.416\" repeatCount=\"indefinite\"/>\n                          <animate attributeName=\"stroke-dashoffset\" dur=\"2s\" values=\"0;-15.708;-31.416\" repeatCount=\"indefinite\"/>\n                        </circle>\n                      </svg>\n                    </span>\n                  ) : (\n                    <span className={`${styles.dropdownIcon} ${activeDropdown === 'collections' ? styles.rotated : ''}`}>\n                      <svg width=\"12\" height=\"8\" viewBox=\"0 0 12 8\" fill=\"none\">\n                        <path d=\"M1 1.5L6 6.5L11 1.5\" stroke=\"currentColor\" strokeWidth=\"1.5\" strokeLinecap=\"round\" strokeLinejoin=\"round\"/>\n                      </svg>\n                    </span>\n                  )}\n                </button>\n                {activeDropdown === 'collections' && !isLoading && (\n                  <div className={styles.dropdown}>\n                    <ul className={styles.dropdownList}>\n                      {collectionItems.map((item, index) => (\n                        <li key={index} className={styles.dropdownItem}>\n                          <Link href={item.href} className={styles.dropdownLink}>\n                            {item.label}\n                          </Link>\n                          {item.children && item.children.length > 0 && (\n                            <ul className={styles.subDropdownList}>\n                              {item.children.map((child, childIndex) => (\n                                <li key={childIndex} className={styles.subDropdownItem}>\n                                  <Link href={child.href} className={styles.subDropdownLink}>\n                                    {child.label}\n                                  </Link>\n                                </li>\n                              ))}\n                            </ul>\n                          )}\n                        </li>\n                      ))}\n                    </ul>\n                  </div>\n                )}\n              </div>\n            </li>\n\n            {/* Completed Projects */}\n            <li className={styles.navItem}>\n              <Link href=\"/completed-projects\" className={styles.navLink}>\n                Completed Projects\n              </Link>\n            </li>\n\n            {/* Discover Dropdown */}\n            <li className={styles.navItem}>\n              <div\n                className={styles.dropdownContainer}\n                ref={el => dropdownRefs.current['discover'] = el}\n              >\n                <button\n                  className={`${styles.navButton} ${activeDropdown === 'discover' ? styles.active : ''}`}\n                  onClick={() => handleDropdownToggle('discover')}\n                  aria-expanded={activeDropdown === 'discover'}\n                >\n                  Discover\n                  <span className={`${styles.dropdownIcon} ${activeDropdown === 'discover' ? styles.rotated : ''}`}>\n                    <svg width=\"12\" height=\"8\" viewBox=\"0 0 12 8\" fill=\"none\">\n                      <path d=\"M1 1.5L6 6.5L11 1.5\" stroke=\"currentColor\" strokeWidth=\"1.5\" strokeLinecap=\"round\" strokeLinejoin=\"round\"/>\n                    </svg>\n                  </span>\n                </button>\n                {activeDropdown === 'discover' && (\n                  <div className={styles.dropdown}>\n                    <ul className={styles.dropdownList}>\n                      {discoverItems.map((item, index) => (\n                        <li key={index} className={styles.dropdownItem}>\n                          <Link href={item.href} className={styles.dropdownLink}>\n                            {item.label}\n                          </Link>\n                        </li>\n                      ))}\n                    </ul>\n                  </div>\n                )}\n              </div>\n            </li>\n          </ul>\n        </nav>\n\n        {/* Cart Icon */}\n        <div className={styles.cartContainer}>\n          <Link href=\"/cart\" className={styles.cartLink}>\n            <svg width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" strokeWidth=\"1.5\">\n              <path d=\"M3 3H5L5.4 5M7 13H17L21 5H5.4M7 13L5.4 5M7 13L4.7 15.3C4.3 15.7 4.6 16.5 5.1 16.5H17M17 13V17C17 18.1 16.1 19 15 19H9C7.9 19 7 18.1 7 17V13M17 13H7\"/>\n            </svg>\n          </Link>\n        </div>\n      </div>\n    </header>\n  );\n};\n\nexport default Header;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAGA;;;AAPA;;;;;AAaA,MAAM,SAAgC,CAAC,EAAE,QAAQ,YAAY,EAAE;;IAC7D,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAyB,EAAE;IACxE,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IACpE,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,eAAe,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAA4C,CAAC;IAEvE,yBAAyB;IACzB,MAAM,eAA+B;QACnC;YAAE,OAAO;YAAc,MAAM;QAAW;QACxC;YAAE,OAAO;YAAa,MAAM;QAAa;QACzC;YAAE,OAAO;YAAkB,MAAM;QAAkB;QACnD;YAAE,OAAO;YAAoB,MAAM;QAAoB;KACxD;IAED,0BAA0B;IAC1B,MAAM,gBAAgC;QACpC;YAAE,OAAO;YAAW,MAAM;QAAW;QACrC;YAAE,OAAO;YAAY,MAAM;QAAY;QACvC;YAAE,OAAO;YAAU,MAAM;QAAU;QACnC;YAAE,OAAO;YAAkB,MAAM;QAAkB;QACnD;YAAE,OAAO;YAAO,MAAM;QAAO;KAC9B;IAED,uCAAuC;IACvC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;4BAAE;YACR,MAAM;qDAAmB;oBACvB,IAAI;wBACF,aAAa;wBACb,MAAM,gBAAgB,MAAM,+IAAA,CAAA,uBAAoB,CAAC,YAAY;wBAC7D,eAAe;oBACjB,EAAE,OAAO,OAAO;wBACd,QAAQ,KAAK,CAAC,gCAAgC;oBAChD,SAAU;wBACR,aAAa;oBACf;gBACF;;YAEA;QACF;2BAAG,EAAE;IAEL,yBAAyB;IACzB,MAAM,uBAAuB,CAAC;QAC5B,kBAAkB,mBAAmB,eAAe,OAAO;IAC7D;IAEA,yCAAyC;IACzC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;4BAAE;YACR,MAAM;uDAAqB,CAAC;oBAC1B,MAAM,SAAS,MAAM,MAAM;oBAC3B,MAAM,wBAAwB,OAAO,MAAM,CAAC,aAAa,OAAO,EAAE,IAAI;qFACpE,CAAA,MAAO,OAAO,IAAI,QAAQ,CAAC;;oBAG7B,IAAI,CAAC,uBAAuB;wBAC1B,kBAAkB;oBACpB;gBACF;;YAEA,SAAS,gBAAgB,CAAC,aAAa;YACvC;oCAAO,IAAM,SAAS,mBAAmB,CAAC,aAAa;;QACzD;2BAAG,EAAE;IAEL,wCAAwC;IACxC,MAAM,6BAA6B,CAAC;QAClC,OAAO,YAAY,GAAG,CAAC,CAAA,aAAc,CAAC;gBACpC,OAAO,WAAW,IAAI;gBACtB,MAAM,CAAC,aAAa,EAAE,WAAW,EAAE,EAAE;gBACrC,UAAU,WAAW,QAAQ,CAAC,MAAM,GAAG,IACnC,2BAA2B,WAAW,QAAQ,IAC9C;YACN,CAAC;IACH;IAEA,MAAM,kBAAkB,2BAA2B;IAEnD,qBACE,6LAAC;QAAO,WAAW,8JAAA,CAAA,UAAM,CAAC,MAAM;kBAC9B,cAAA,6LAAC;YAAI,WAAW,8JAAA,CAAA,UAAM,CAAC,SAAS;;8BAE9B,6LAAC;oBAAI,WAAW,8JAAA,CAAA,UAAM,CAAC,IAAI;8BACzB,cAAA,6LAAC,+JAAA,CAAA,UAAI;wBAAC,MAAK;wBAAI,WAAW,8JAAA,CAAA,UAAM,CAAC,QAAQ;;0CACvC,6LAAC;gCAAK,WAAW,8JAAA,CAAA,UAAM,CAAC,QAAQ;0CAAG;;;;;;0CACnC,6LAAC;gCAAK,WAAW,8JAAA,CAAA,UAAM,CAAC,WAAW;0CAAE;;;;;;;;;;;;;;;;;8BAKzC,6LAAC;oBAAI,WAAW,8JAAA,CAAA,UAAM,CAAC,GAAG;8BACxB,cAAA,6LAAC;wBAAG,WAAW,8JAAA,CAAA,UAAM,CAAC,OAAO;;0CAE3B,6LAAC;gCAAG,WAAW,8JAAA,CAAA,UAAM,CAAC,OAAO;0CAC3B,cAAA,6LAAC;oCACC,WAAW,8JAAA,CAAA,UAAM,CAAC,iBAAiB;oCACnC,KAAK,CAAA,KAAM,aAAa,OAAO,CAAC,UAAU,GAAG;;sDAE7C,6LAAC;4CACC,WAAW,GAAG,8JAAA,CAAA,UAAM,CAAC,SAAS,CAAC,CAAC,EAAE,mBAAmB,YAAY,8JAAA,CAAA,UAAM,CAAC,MAAM,GAAG,IAAI;4CACrF,SAAS,IAAM,qBAAqB;4CACpC,iBAAe,mBAAmB;;gDACnC;8DAEC,6LAAC;oDAAK,WAAW,GAAG,8JAAA,CAAA,UAAM,CAAC,YAAY,CAAC,CAAC,EAAE,mBAAmB,YAAY,8JAAA,CAAA,UAAM,CAAC,OAAO,GAAG,IAAI;8DAC7F,cAAA,6LAAC;wDAAI,OAAM;wDAAK,QAAO;wDAAI,SAAQ;wDAAW,MAAK;kEACjD,cAAA,6LAAC;4DAAK,GAAE;4DAAsB,QAAO;4DAAe,aAAY;4DAAM,eAAc;4DAAQ,gBAAe;;;;;;;;;;;;;;;;;;;;;;wCAIhH,mBAAmB,2BAClB,6LAAC;4CAAI,WAAW,8JAAA,CAAA,UAAM,CAAC,QAAQ;sDAC7B,cAAA,6LAAC;gDAAG,WAAW,8JAAA,CAAA,UAAM,CAAC,YAAY;0DAC/B,aAAa,GAAG,CAAC,CAAC,MAAM,sBACvB,6LAAC;wDAAe,WAAW,8JAAA,CAAA,UAAM,CAAC,YAAY;kEAC5C,cAAA,6LAAC,+JAAA,CAAA,UAAI;4DAAC,MAAM,KAAK,IAAI;4DAAE,WAAW,8JAAA,CAAA,UAAM,CAAC,YAAY;sEAClD,KAAK,KAAK;;;;;;uDAFN;;;;;;;;;;;;;;;;;;;;;;;;;;0CAarB,6LAAC;gCAAG,WAAW,8JAAA,CAAA,UAAM,CAAC,OAAO;0CAC3B,cAAA,6LAAC,+JAAA,CAAA,UAAI;oCAAC,MAAK;oCAAY,WAAW,8JAAA,CAAA,UAAM,CAAC,OAAO;8CAAE;;;;;;;;;;;0CAMpD,6LAAC;gCAAG,WAAW,8JAAA,CAAA,UAAM,CAAC,OAAO;0CAC3B,cAAA,6LAAC;oCACC,WAAW,8JAAA,CAAA,UAAM,CAAC,iBAAiB;oCACnC,KAAK,CAAA,KAAM,aAAa,OAAO,CAAC,cAAc,GAAG;;sDAEjD,6LAAC;4CACC,WAAW,GAAG,8JAAA,CAAA,UAAM,CAAC,SAAS,CAAC,CAAC,EAAE,mBAAmB,gBAAgB,8JAAA,CAAA,UAAM,CAAC,MAAM,GAAG,IAAI;4CACzF,SAAS,IAAM,qBAAqB;4CACpC,iBAAe,mBAAmB;4CAClC,UAAU;;gDACX;gDAEE,0BACC,6LAAC;oDAAK,WAAW,8JAAA,CAAA,UAAM,CAAC,WAAW;8DACjC,cAAA,6LAAC;wDAAI,OAAM;wDAAK,QAAO;wDAAK,SAAQ;wDAAY,MAAK;kEACnD,cAAA,6LAAC;4DAAO,IAAG;4DAAK,IAAG;4DAAK,GAAE;4DAAK,QAAO;4DAAe,aAAY;4DAAI,eAAc;4DAAQ,iBAAgB;4DAAS,kBAAiB;;8EACnI,6LAAC;oEAAQ,eAAc;oEAAmB,KAAI;oEAAK,QAAO;oEAAkC,aAAY;;;;;;8EACxG,6LAAC;oEAAQ,eAAc;oEAAoB,KAAI;oEAAK,QAAO;oEAAoB,aAAY;;;;;;;;;;;;;;;;;;;;;yEAKjG,6LAAC;oDAAK,WAAW,GAAG,8JAAA,CAAA,UAAM,CAAC,YAAY,CAAC,CAAC,EAAE,mBAAmB,gBAAgB,8JAAA,CAAA,UAAM,CAAC,OAAO,GAAG,IAAI;8DACjG,cAAA,6LAAC;wDAAI,OAAM;wDAAK,QAAO;wDAAI,SAAQ;wDAAW,MAAK;kEACjD,cAAA,6LAAC;4DAAK,GAAE;4DAAsB,QAAO;4DAAe,aAAY;4DAAM,eAAc;4DAAQ,gBAAe;;;;;;;;;;;;;;;;;;;;;;wCAKlH,mBAAmB,iBAAiB,CAAC,2BACpC,6LAAC;4CAAI,WAAW,8JAAA,CAAA,UAAM,CAAC,QAAQ;sDAC7B,cAAA,6LAAC;gDAAG,WAAW,8JAAA,CAAA,UAAM,CAAC,YAAY;0DAC/B,gBAAgB,GAAG,CAAC,CAAC,MAAM,sBAC1B,6LAAC;wDAAe,WAAW,8JAAA,CAAA,UAAM,CAAC,YAAY;;0EAC5C,6LAAC,+JAAA,CAAA,UAAI;gEAAC,MAAM,KAAK,IAAI;gEAAE,WAAW,8JAAA,CAAA,UAAM,CAAC,YAAY;0EAClD,KAAK,KAAK;;;;;;4DAEZ,KAAK,QAAQ,IAAI,KAAK,QAAQ,CAAC,MAAM,GAAG,mBACvC,6LAAC;gEAAG,WAAW,8JAAA,CAAA,UAAM,CAAC,eAAe;0EAClC,KAAK,QAAQ,CAAC,GAAG,CAAC,CAAC,OAAO,2BACzB,6LAAC;wEAAoB,WAAW,8JAAA,CAAA,UAAM,CAAC,eAAe;kFACpD,cAAA,6LAAC,+JAAA,CAAA,UAAI;4EAAC,MAAM,MAAM,IAAI;4EAAE,WAAW,8JAAA,CAAA,UAAM,CAAC,eAAe;sFACtD,MAAM,KAAK;;;;;;uEAFP;;;;;;;;;;;uDAPR;;;;;;;;;;;;;;;;;;;;;;;;;;0CAwBrB,6LAAC;gCAAG,WAAW,8JAAA,CAAA,UAAM,CAAC,OAAO;0CAC3B,cAAA,6LAAC,+JAAA,CAAA,UAAI;oCAAC,MAAK;oCAAsB,WAAW,8JAAA,CAAA,UAAM,CAAC,OAAO;8CAAE;;;;;;;;;;;0CAM9D,6LAAC;gCAAG,WAAW,8JAAA,CAAA,UAAM,CAAC,OAAO;0CAC3B,cAAA,6LAAC;oCACC,WAAW,8JAAA,CAAA,UAAM,CAAC,iBAAiB;oCACnC,KAAK,CAAA,KAAM,aAAa,OAAO,CAAC,WAAW,GAAG;;sDAE9C,6LAAC;4CACC,WAAW,GAAG,8JAAA,CAAA,UAAM,CAAC,SAAS,CAAC,CAAC,EAAE,mBAAmB,aAAa,8JAAA,CAAA,UAAM,CAAC,MAAM,GAAG,IAAI;4CACtF,SAAS,IAAM,qBAAqB;4CACpC,iBAAe,mBAAmB;;gDACnC;8DAEC,6LAAC;oDAAK,WAAW,GAAG,8JAAA,CAAA,UAAM,CAAC,YAAY,CAAC,CAAC,EAAE,mBAAmB,aAAa,8JAAA,CAAA,UAAM,CAAC,OAAO,GAAG,IAAI;8DAC9F,cAAA,6LAAC;wDAAI,OAAM;wDAAK,QAAO;wDAAI,SAAQ;wDAAW,MAAK;kEACjD,cAAA,6LAAC;4DAAK,GAAE;4DAAsB,QAAO;4DAAe,aAAY;4DAAM,eAAc;4DAAQ,gBAAe;;;;;;;;;;;;;;;;;;;;;;wCAIhH,mBAAmB,4BAClB,6LAAC;4CAAI,WAAW,8JAAA,CAAA,UAAM,CAAC,QAAQ;sDAC7B,cAAA,6LAAC;gDAAG,WAAW,8JAAA,CAAA,UAAM,CAAC,YAAY;0DAC/B,cAAc,GAAG,CAAC,CAAC,MAAM,sBACxB,6LAAC;wDAAe,WAAW,8JAAA,CAAA,UAAM,CAAC,YAAY;kEAC5C,cAAA,6LAAC,+JAAA,CAAA,UAAI;4DAAC,MAAM,KAAK,IAAI;4DAAE,WAAW,8JAAA,CAAA,UAAM,CAAC,YAAY;sEAClD,KAAK,KAAK;;;;;;uDAFN;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAezB,6LAAC;oBAAI,WAAW,8JAAA,CAAA,UAAM,CAAC,aAAa;8BAClC,cAAA,6LAAC,+JAAA,CAAA,UAAI;wBAAC,MAAK;wBAAQ,WAAW,8JAAA,CAAA,UAAM,CAAC,QAAQ;kCAC3C,cAAA,6LAAC;4BAAI,OAAM;4BAAK,QAAO;4BAAK,SAAQ;4BAAY,MAAK;4BAAO,QAAO;4BAAe,aAAY;sCAC5F,cAAA,6LAAC;gCAAK,GAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOtB;GAjPM;KAAA;uCAmPS", "debugId": null}}, {"offset": {"line": 1601, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/cast-stonev2/cast-stonev2/Frontend/cast-stone-frontend/node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js"], "sourcesContent": ["/**\n * @license React\n * react-jsx-dev-runtime.development.js\n *\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\n\"use strict\";\n\"production\" !== process.env.NODE_ENV &&\n  (function () {\n    function getComponentNameFromType(type) {\n      if (null == type) return null;\n      if (\"function\" === typeof type)\n        return type.$$typeof === REACT_CLIENT_REFERENCE\n          ? null\n          : type.displayName || type.name || null;\n      if (\"string\" === typeof type) return type;\n      switch (type) {\n        case REACT_FRAGMENT_TYPE:\n          return \"Fragment\";\n        case REACT_PROFILER_TYPE:\n          return \"Profiler\";\n        case REACT_STRICT_MODE_TYPE:\n          return \"StrictMode\";\n        case REACT_SUSPENSE_TYPE:\n          return \"Suspense\";\n        case REACT_SUSPENSE_LIST_TYPE:\n          return \"SuspenseList\";\n        case REACT_ACTIVITY_TYPE:\n          return \"Activity\";\n      }\n      if (\"object\" === typeof type)\n        switch (\n          (\"number\" === typeof type.tag &&\n            console.error(\n              \"Received an unexpected object in getComponentNameFromType(). This is likely a bug in React. Please file an issue.\"\n            ),\n          type.$$typeof)\n        ) {\n          case REACT_PORTAL_TYPE:\n            return \"Portal\";\n          case REACT_CONTEXT_TYPE:\n            return (type.displayName || \"Context\") + \".Provider\";\n          case REACT_CONSUMER_TYPE:\n            return (type._context.displayName || \"Context\") + \".Consumer\";\n          case REACT_FORWARD_REF_TYPE:\n            var innerType = type.render;\n            type = type.displayName;\n            type ||\n              ((type = innerType.displayName || innerType.name || \"\"),\n              (type = \"\" !== type ? \"ForwardRef(\" + type + \")\" : \"ForwardRef\"));\n            return type;\n          case REACT_MEMO_TYPE:\n            return (\n              (innerType = type.displayName || null),\n              null !== innerType\n                ? innerType\n                : getComponentNameFromType(type.type) || \"Memo\"\n            );\n          case REACT_LAZY_TYPE:\n            innerType = type._payload;\n            type = type._init;\n            try {\n              return getComponentNameFromType(type(innerType));\n            } catch (x) {}\n        }\n      return null;\n    }\n    function testStringCoercion(value) {\n      return \"\" + value;\n    }\n    function checkKeyStringCoercion(value) {\n      try {\n        testStringCoercion(value);\n        var JSCompiler_inline_result = !1;\n      } catch (e) {\n        JSCompiler_inline_result = !0;\n      }\n      if (JSCompiler_inline_result) {\n        JSCompiler_inline_result = console;\n        var JSCompiler_temp_const = JSCompiler_inline_result.error;\n        var JSCompiler_inline_result$jscomp$0 =\n          (\"function\" === typeof Symbol &&\n            Symbol.toStringTag &&\n            value[Symbol.toStringTag]) ||\n          value.constructor.name ||\n          \"Object\";\n        JSCompiler_temp_const.call(\n          JSCompiler_inline_result,\n          \"The provided key is an unsupported type %s. This value must be coerced to a string before using it here.\",\n          JSCompiler_inline_result$jscomp$0\n        );\n        return testStringCoercion(value);\n      }\n    }\n    function getTaskName(type) {\n      if (type === REACT_FRAGMENT_TYPE) return \"<>\";\n      if (\n        \"object\" === typeof type &&\n        null !== type &&\n        type.$$typeof === REACT_LAZY_TYPE\n      )\n        return \"<...>\";\n      try {\n        var name = getComponentNameFromType(type);\n        return name ? \"<\" + name + \">\" : \"<...>\";\n      } catch (x) {\n        return \"<...>\";\n      }\n    }\n    function getOwner() {\n      var dispatcher = ReactSharedInternals.A;\n      return null === dispatcher ? null : dispatcher.getOwner();\n    }\n    function UnknownOwner() {\n      return Error(\"react-stack-top-frame\");\n    }\n    function hasValidKey(config) {\n      if (hasOwnProperty.call(config, \"key\")) {\n        var getter = Object.getOwnPropertyDescriptor(config, \"key\").get;\n        if (getter && getter.isReactWarning) return !1;\n      }\n      return void 0 !== config.key;\n    }\n    function defineKeyPropWarningGetter(props, displayName) {\n      function warnAboutAccessingKey() {\n        specialPropKeyWarningShown ||\n          ((specialPropKeyWarningShown = !0),\n          console.error(\n            \"%s: `key` is not a prop. Trying to access it will result in `undefined` being returned. If you need to access the same value within the child component, you should pass it as a different prop. (https://react.dev/link/special-props)\",\n            displayName\n          ));\n      }\n      warnAboutAccessingKey.isReactWarning = !0;\n      Object.defineProperty(props, \"key\", {\n        get: warnAboutAccessingKey,\n        configurable: !0\n      });\n    }\n    function elementRefGetterWithDeprecationWarning() {\n      var componentName = getComponentNameFromType(this.type);\n      didWarnAboutElementRef[componentName] ||\n        ((didWarnAboutElementRef[componentName] = !0),\n        console.error(\n          \"Accessing element.ref was removed in React 19. ref is now a regular prop. It will be removed from the JSX Element type in a future release.\"\n        ));\n      componentName = this.props.ref;\n      return void 0 !== componentName ? componentName : null;\n    }\n    function ReactElement(\n      type,\n      key,\n      self,\n      source,\n      owner,\n      props,\n      debugStack,\n      debugTask\n    ) {\n      self = props.ref;\n      type = {\n        $$typeof: REACT_ELEMENT_TYPE,\n        type: type,\n        key: key,\n        props: props,\n        _owner: owner\n      };\n      null !== (void 0 !== self ? self : null)\n        ? Object.defineProperty(type, \"ref\", {\n            enumerable: !1,\n            get: elementRefGetterWithDeprecationWarning\n          })\n        : Object.defineProperty(type, \"ref\", { enumerable: !1, value: null });\n      type._store = {};\n      Object.defineProperty(type._store, \"validated\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: 0\n      });\n      Object.defineProperty(type, \"_debugInfo\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: null\n      });\n      Object.defineProperty(type, \"_debugStack\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: debugStack\n      });\n      Object.defineProperty(type, \"_debugTask\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: debugTask\n      });\n      Object.freeze && (Object.freeze(type.props), Object.freeze(type));\n      return type;\n    }\n    function jsxDEVImpl(\n      type,\n      config,\n      maybeKey,\n      isStaticChildren,\n      source,\n      self,\n      debugStack,\n      debugTask\n    ) {\n      var children = config.children;\n      if (void 0 !== children)\n        if (isStaticChildren)\n          if (isArrayImpl(children)) {\n            for (\n              isStaticChildren = 0;\n              isStaticChildren < children.length;\n              isStaticChildren++\n            )\n              validateChildKeys(children[isStaticChildren]);\n            Object.freeze && Object.freeze(children);\n          } else\n            console.error(\n              \"React.jsx: Static children should always be an array. You are likely explicitly calling React.jsxs or React.jsxDEV. Use the Babel transform instead.\"\n            );\n        else validateChildKeys(children);\n      if (hasOwnProperty.call(config, \"key\")) {\n        children = getComponentNameFromType(type);\n        var keys = Object.keys(config).filter(function (k) {\n          return \"key\" !== k;\n        });\n        isStaticChildren =\n          0 < keys.length\n            ? \"{key: someKey, \" + keys.join(\": ..., \") + \": ...}\"\n            : \"{key: someKey}\";\n        didWarnAboutKeySpread[children + isStaticChildren] ||\n          ((keys =\n            0 < keys.length ? \"{\" + keys.join(\": ..., \") + \": ...}\" : \"{}\"),\n          console.error(\n            'A props object containing a \"key\" prop is being spread into JSX:\\n  let props = %s;\\n  <%s {...props} />\\nReact keys must be passed directly to JSX without using spread:\\n  let props = %s;\\n  <%s key={someKey} {...props} />',\n            isStaticChildren,\n            children,\n            keys,\n            children\n          ),\n          (didWarnAboutKeySpread[children + isStaticChildren] = !0));\n      }\n      children = null;\n      void 0 !== maybeKey &&\n        (checkKeyStringCoercion(maybeKey), (children = \"\" + maybeKey));\n      hasValidKey(config) &&\n        (checkKeyStringCoercion(config.key), (children = \"\" + config.key));\n      if (\"key\" in config) {\n        maybeKey = {};\n        for (var propName in config)\n          \"key\" !== propName && (maybeKey[propName] = config[propName]);\n      } else maybeKey = config;\n      children &&\n        defineKeyPropWarningGetter(\n          maybeKey,\n          \"function\" === typeof type\n            ? type.displayName || type.name || \"Unknown\"\n            : type\n        );\n      return ReactElement(\n        type,\n        children,\n        self,\n        source,\n        getOwner(),\n        maybeKey,\n        debugStack,\n        debugTask\n      );\n    }\n    function validateChildKeys(node) {\n      \"object\" === typeof node &&\n        null !== node &&\n        node.$$typeof === REACT_ELEMENT_TYPE &&\n        node._store &&\n        (node._store.validated = 1);\n    }\n    var React = require(\"next/dist/compiled/react\"),\n      REACT_ELEMENT_TYPE = Symbol.for(\"react.transitional.element\"),\n      REACT_PORTAL_TYPE = Symbol.for(\"react.portal\"),\n      REACT_FRAGMENT_TYPE = Symbol.for(\"react.fragment\"),\n      REACT_STRICT_MODE_TYPE = Symbol.for(\"react.strict_mode\"),\n      REACT_PROFILER_TYPE = Symbol.for(\"react.profiler\");\n    Symbol.for(\"react.provider\");\n    var REACT_CONSUMER_TYPE = Symbol.for(\"react.consumer\"),\n      REACT_CONTEXT_TYPE = Symbol.for(\"react.context\"),\n      REACT_FORWARD_REF_TYPE = Symbol.for(\"react.forward_ref\"),\n      REACT_SUSPENSE_TYPE = Symbol.for(\"react.suspense\"),\n      REACT_SUSPENSE_LIST_TYPE = Symbol.for(\"react.suspense_list\"),\n      REACT_MEMO_TYPE = Symbol.for(\"react.memo\"),\n      REACT_LAZY_TYPE = Symbol.for(\"react.lazy\"),\n      REACT_ACTIVITY_TYPE = Symbol.for(\"react.activity\"),\n      REACT_CLIENT_REFERENCE = Symbol.for(\"react.client.reference\"),\n      ReactSharedInternals =\n        React.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,\n      hasOwnProperty = Object.prototype.hasOwnProperty,\n      isArrayImpl = Array.isArray,\n      createTask = console.createTask\n        ? console.createTask\n        : function () {\n            return null;\n          };\n    React = {\n      \"react-stack-bottom-frame\": function (callStackForError) {\n        return callStackForError();\n      }\n    };\n    var specialPropKeyWarningShown;\n    var didWarnAboutElementRef = {};\n    var unknownOwnerDebugStack = React[\"react-stack-bottom-frame\"].bind(\n      React,\n      UnknownOwner\n    )();\n    var unknownOwnerDebugTask = createTask(getTaskName(UnknownOwner));\n    var didWarnAboutKeySpread = {};\n    exports.Fragment = REACT_FRAGMENT_TYPE;\n    exports.jsxDEV = function (\n      type,\n      config,\n      maybeKey,\n      isStaticChildren,\n      source,\n      self\n    ) {\n      var trackActualOwner =\n        1e4 > ReactSharedInternals.recentlyCreatedOwnerStacks++;\n      return jsxDEVImpl(\n        type,\n        config,\n        maybeKey,\n        isStaticChildren,\n        source,\n        self,\n        trackActualOwner\n          ? Error(\"react-stack-top-frame\")\n          : unknownOwnerDebugStack,\n        trackActualOwner ? createTask(getTaskName(type)) : unknownOwnerDebugTask\n      );\n    };\n  })();\n"], "names": [], "mappings": "AAAA;;;;;;;;CAQC,GAGgB;AADjB;AACA,oEACE,AAAC;IACC,SAAS,yBAAyB,IAAI;QACpC,IAAI,QAAQ,MAAM,OAAO;QACzB,IAAI,eAAe,OAAO,MACxB,OAAO,KAAK,QAAQ,KAAK,yBACrB,OACA,KAAK,WAAW,IAAI,KAAK,IAAI,IAAI;QACvC,IAAI,aAAa,OAAO,MAAM,OAAO;QACrC,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;QACX;QACA,IAAI,aAAa,OAAO,MACtB,OACG,aAAa,OAAO,KAAK,GAAG,IAC3B,QAAQ,KAAK,CACX,sHAEJ,KAAK,QAAQ;YAEb,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO,CAAC,KAAK,WAAW,IAAI,SAAS,IAAI;YAC3C,KAAK;gBACH,OAAO,CAAC,KAAK,QAAQ,CAAC,WAAW,IAAI,SAAS,IAAI;YACpD,KAAK;gBACH,IAAI,YAAY,KAAK,MAAM;gBAC3B,OAAO,KAAK,WAAW;gBACvB,QACE,CAAC,AAAC,OAAO,UAAU,WAAW,IAAI,UAAU,IAAI,IAAI,IACnD,OAAO,OAAO,OAAO,gBAAgB,OAAO,MAAM,YAAa;gBAClE,OAAO;YACT,KAAK;gBACH,OACE,AAAC,YAAY,KAAK,WAAW,IAAI,MACjC,SAAS,YACL,YACA,yBAAyB,KAAK,IAAI,KAAK;YAE/C,KAAK;gBACH,YAAY,KAAK,QAAQ;gBACzB,OAAO,KAAK,KAAK;gBACjB,IAAI;oBACF,OAAO,yBAAyB,KAAK;gBACvC,EAAE,OAAO,GAAG,CAAC;QACjB;QACF,OAAO;IACT;IACA,SAAS,mBAAmB,KAAK;QAC/B,OAAO,KAAK;IACd;IACA,SAAS,uBAAuB,KAAK;QACnC,IAAI;YACF,mBAAmB;YACnB,IAAI,2BAA2B,CAAC;QAClC,EAAE,OAAO,GAAG;YACV,2BAA2B,CAAC;QAC9B;QACA,IAAI,0BAA0B;YAC5B,2BAA2B;YAC3B,IAAI,wBAAwB,yBAAyB,KAAK;YAC1D,IAAI,oCACF,AAAC,eAAe,OAAO,UACrB,OAAO,WAAW,IAClB,KAAK,CAAC,OAAO,WAAW,CAAC,IAC3B,MAAM,WAAW,CAAC,IAAI,IACtB;YACF,sBAAsB,IAAI,CACxB,0BACA,4GACA;YAEF,OAAO,mBAAmB;QAC5B;IACF;IACA,SAAS,YAAY,IAAI;QACvB,IAAI,SAAS,qBAAqB,OAAO;QACzC,IACE,aAAa,OAAO,QACpB,SAAS,QACT,KAAK,QAAQ,KAAK,iBAElB,OAAO;QACT,IAAI;YACF,IAAI,OAAO,yBAAyB;YACpC,OAAO,OAAO,MAAM,OAAO,MAAM;QACnC,EAAE,OAAO,GAAG;YACV,OAAO;QACT;IACF;IACA,SAAS;QACP,IAAI,aAAa,qBAAqB,CAAC;QACvC,OAAO,SAAS,aAAa,OAAO,WAAW,QAAQ;IACzD;IACA,SAAS;QACP,OAAO,MAAM;IACf;IACA,SAAS,YAAY,MAAM;QACzB,IAAI,eAAe,IAAI,CAAC,QAAQ,QAAQ;YACtC,IAAI,SAAS,OAAO,wBAAwB,CAAC,QAAQ,OAAO,GAAG;YAC/D,IAAI,UAAU,OAAO,cAAc,EAAE,OAAO,CAAC;QAC/C;QACA,OAAO,KAAK,MAAM,OAAO,GAAG;IAC9B;IACA,SAAS,2BAA2B,KAAK,EAAE,WAAW;QACpD,SAAS;YACP,8BACE,CAAC,AAAC,6BAA6B,CAAC,GAChC,QAAQ,KAAK,CACX,2OACA,YACD;QACL;QACA,sBAAsB,cAAc,GAAG,CAAC;QACxC,OAAO,cAAc,CAAC,OAAO,OAAO;YAClC,KAAK;YACL,cAAc,CAAC;QACjB;IACF;IACA,SAAS;QACP,IAAI,gBAAgB,yBAAyB,IAAI,CAAC,IAAI;QACtD,sBAAsB,CAAC,cAAc,IACnC,CAAC,AAAC,sBAAsB,CAAC,cAAc,GAAG,CAAC,GAC3C,QAAQ,KAAK,CACX,8IACD;QACH,gBAAgB,IAAI,CAAC,KAAK,CAAC,GAAG;QAC9B,OAAO,KAAK,MAAM,gBAAgB,gBAAgB;IACpD;IACA,SAAS,aACP,IAAI,EACJ,GAAG,EACH,IAAI,EACJ,MAAM,EACN,KAAK,EACL,KAAK,EACL,UAAU,EACV,SAAS;QAET,OAAO,MAAM,GAAG;QAChB,OAAO;YACL,UAAU;YACV,MAAM;YACN,KAAK;YACL,OAAO;YACP,QAAQ;QACV;QACA,SAAS,CAAC,KAAK,MAAM,OAAO,OAAO,IAAI,IACnC,OAAO,cAAc,CAAC,MAAM,OAAO;YACjC,YAAY,CAAC;YACb,KAAK;QACP,KACA,OAAO,cAAc,CAAC,MAAM,OAAO;YAAE,YAAY,CAAC;YAAG,OAAO;QAAK;QACrE,KAAK,MAAM,GAAG,CAAC;QACf,OAAO,cAAc,CAAC,KAAK,MAAM,EAAE,aAAa;YAC9C,cAAc,CAAC;YACf,YAAY,CAAC;YACb,UAAU,CAAC;YACX,OAAO;QACT;QACA,OAAO,cAAc,CAAC,MAAM,cAAc;YACxC,cAAc,CAAC;YACf,YAAY,CAAC;YACb,UAAU,CAAC;YACX,OAAO;QACT;QACA,OAAO,cAAc,CAAC,MAAM,eAAe;YACzC,cAAc,CAAC;YACf,YAAY,CAAC;YACb,UAAU,CAAC;YACX,OAAO;QACT;QACA,OAAO,cAAc,CAAC,MAAM,cAAc;YACxC,cAAc,CAAC;YACf,YAAY,CAAC;YACb,UAAU,CAAC;YACX,OAAO;QACT;QACA,OAAO,MAAM,IAAI,CAAC,OAAO,MAAM,CAAC,KAAK,KAAK,GAAG,OAAO,MAAM,CAAC,KAAK;QAChE,OAAO;IACT;IACA,SAAS,WACP,IAAI,EACJ,MAAM,EACN,QAAQ,EACR,gBAAgB,EAChB,MAAM,EACN,IAAI,EACJ,UAAU,EACV,SAAS;QAET,IAAI,WAAW,OAAO,QAAQ;QAC9B,IAAI,KAAK,MAAM,UACb,IAAI,kBACF,IAAI,YAAY,WAAW;YACzB,IACE,mBAAmB,GACnB,mBAAmB,SAAS,MAAM,EAClC,mBAEA,kBAAkB,QAAQ,CAAC,iBAAiB;YAC9C,OAAO,MAAM,IAAI,OAAO,MAAM,CAAC;QACjC,OACE,QAAQ,KAAK,CACX;aAED,kBAAkB;QACzB,IAAI,eAAe,IAAI,CAAC,QAAQ,QAAQ;YACtC,WAAW,yBAAyB;YACpC,IAAI,OAAO,OAAO,IAAI,CAAC,QAAQ,MAAM,CAAC,SAAU,CAAC;gBAC/C,OAAO,UAAU;YACnB;YACA,mBACE,IAAI,KAAK,MAAM,GACX,oBAAoB,KAAK,IAAI,CAAC,aAAa,WAC3C;YACN,qBAAqB,CAAC,WAAW,iBAAiB,IAChD,CAAC,AAAC,OACA,IAAI,KAAK,MAAM,GAAG,MAAM,KAAK,IAAI,CAAC,aAAa,WAAW,MAC5D,QAAQ,KAAK,CACX,mOACA,kBACA,UACA,MACA,WAED,qBAAqB,CAAC,WAAW,iBAAiB,GAAG,CAAC,CAAE;QAC7D;QACA,WAAW;QACX,KAAK,MAAM,YACT,CAAC,uBAAuB,WAAY,WAAW,KAAK,QAAS;QAC/D,YAAY,WACV,CAAC,uBAAuB,OAAO,GAAG,GAAI,WAAW,KAAK,OAAO,GAAG,AAAC;QACnE,IAAI,SAAS,QAAQ;YACnB,WAAW,CAAC;YACZ,IAAK,IAAI,YAAY,OACnB,UAAU,YAAY,CAAC,QAAQ,CAAC,SAAS,GAAG,MAAM,CAAC,SAAS;QAChE,OAAO,WAAW;QAClB,YACE,2BACE,UACA,eAAe,OAAO,OAClB,KAAK,WAAW,IAAI,KAAK,IAAI,IAAI,YACjC;QAER,OAAO,aACL,MACA,UACA,MACA,QACA,YACA,UACA,YACA;IAEJ;IACA,SAAS,kBAAkB,IAAI;QAC7B,aAAa,OAAO,QAClB,SAAS,QACT,KAAK,QAAQ,KAAK,sBAClB,KAAK,MAAM,IACX,CAAC,KAAK,MAAM,CAAC,SAAS,GAAG,CAAC;IAC9B;IACA,IAAI,uHACF,qBAAqB,OAAO,GAAG,CAAC,+BAChC,oBAAoB,OAAO,GAAG,CAAC,iBAC/B,sBAAsB,OAAO,GAAG,CAAC,mBACjC,yBAAyB,OAAO,GAAG,CAAC,sBACpC,sBAAsB,OAAO,GAAG,CAAC;IACnC,OAAO,GAAG,CAAC;IACX,IAAI,sBAAsB,OAAO,GAAG,CAAC,mBACnC,qBAAqB,OAAO,GAAG,CAAC,kBAChC,yBAAyB,OAAO,GAAG,CAAC,sBACpC,sBAAsB,OAAO,GAAG,CAAC,mBACjC,2BAA2B,OAAO,GAAG,CAAC,wBACtC,kBAAkB,OAAO,GAAG,CAAC,eAC7B,kBAAkB,OAAO,GAAG,CAAC,eAC7B,sBAAsB,OAAO,GAAG,CAAC,mBACjC,yBAAyB,OAAO,GAAG,CAAC,2BACpC,uBACE,MAAM,+DAA+D,EACvE,iBAAiB,OAAO,SAAS,CAAC,cAAc,EAChD,cAAc,MAAM,OAAO,EAC3B,aAAa,QAAQ,UAAU,GAC3B,QAAQ,UAAU,GAClB;QACE,OAAO;IACT;IACN,QAAQ;QACN,4BAA4B,SAAU,iBAAiB;YACrD,OAAO;QACT;IACF;IACA,IAAI;IACJ,IAAI,yBAAyB,CAAC;IAC9B,IAAI,yBAAyB,KAAK,CAAC,2BAA2B,CAAC,IAAI,CACjE,OACA;IAEF,IAAI,wBAAwB,WAAW,YAAY;IACnD,IAAI,wBAAwB,CAAC;IAC7B,QAAQ,QAAQ,GAAG;IACnB,QAAQ,MAAM,GAAG,SACf,IAAI,EACJ,MAAM,EACN,QAAQ,EACR,gBAAgB,EAChB,MAAM,EACN,IAAI;QAEJ,IAAI,mBACF,MAAM,qBAAqB,0BAA0B;QACvD,OAAO,WACL,MACA,QACA,UACA,kBACA,QACA,MACA,mBACI,MAAM,2BACN,wBACJ,mBAAmB,WAAW,YAAY,SAAS;IAEvD;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1809, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/cast-stonev2/cast-stonev2/Frontend/cast-stone-frontend/node_modules/next/dist/compiled/react/jsx-dev-runtime.js"], "sourcesContent": ["'use strict';\n\nif (process.env.NODE_ENV === 'production') {\n  module.exports = require('./cjs/react-jsx-dev-runtime.production.js');\n} else {\n  module.exports = require('./cjs/react-jsx-dev-runtime.development.js');\n}\n"], "names": [], "mappings": "AAEI;AAFJ;AAEA,uCAA2C;;AAE3C,OAAO;IACL,OAAO,OAAO;AAChB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1821, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/cast-stonev2/cast-stonev2/Frontend/cast-stone-frontend/node_modules/next/src/shared/lib/router/utils/querystring.ts"], "sourcesContent": ["import type { ParsedUrlQuery } from 'querystring'\n\nexport function searchParamsToUrlQuery(\n  searchParams: URLSearchParams\n): ParsedUrlQuery {\n  const query: ParsedUrlQuery = {}\n  for (const [key, value] of searchParams.entries()) {\n    const existing = query[key]\n    if (typeof existing === 'undefined') {\n      query[key] = value\n    } else if (Array.isArray(existing)) {\n      existing.push(value)\n    } else {\n      query[key] = [existing, value]\n    }\n  }\n  return query\n}\n\nfunction stringifyUrlQueryParam(param: unknown): string {\n  if (typeof param === 'string') {\n    return param\n  }\n\n  if (\n    (typeof param === 'number' && !isNaN(param)) ||\n    typeof param === 'boolean'\n  ) {\n    return String(param)\n  } else {\n    return ''\n  }\n}\n\nexport function urlQueryToSearchParams(query: ParsedUrlQuery): URLSearchParams {\n  const searchParams = new URLSearchParams()\n  for (const [key, value] of Object.entries(query)) {\n    if (Array.isArray(value)) {\n      for (const item of value) {\n        searchParams.append(key, stringifyUrlQueryParam(item))\n      }\n    } else {\n      searchParams.set(key, stringifyUrlQueryParam(value))\n    }\n  }\n  return searchParams\n}\n\nexport function assign(\n  target: URLSearchParams,\n  ...searchParamsList: URLSearchParams[]\n): URLSearchParams {\n  for (const searchParams of searchParamsList) {\n    for (const key of searchParams.keys()) {\n      target.delete(key)\n    }\n\n    for (const [key, value] of searchParams.entries()) {\n      target.append(key, value)\n    }\n  }\n\n  return target\n}\n"], "names": ["assign", "searchParamsToUrlQuery", "urlQueryToSearchParams", "searchParams", "query", "key", "value", "entries", "existing", "Array", "isArray", "push", "stringifyUrlQueryParam", "param", "isNaN", "String", "URLSearchParams", "Object", "item", "append", "set", "target", "searchParamsList", "keys", "delete"], "mappings": ";;;;;;;;;;;;;;;;IAgDgBA,MAAM,EAAA;eAANA;;IA9CAC,sBAAsB,EAAA;eAAtBA;;IAgCAC,sBAAsB,EAAA;eAAtBA;;;AAhCT,SAASD,uBACdE,YAA6B;IAE7B,MAAMC,QAAwB,CAAC;IAC/B,KAAK,MAAM,CAACC,KAAKC,MAAM,IAAIH,aAAaI,OAAO,GAAI;QACjD,MAAMC,WAAWJ,KAAK,CAACC,IAAI;QAC3B,IAAI,OAAOG,aAAa,aAAa;YACnCJ,KAAK,CAACC,IAAI,GAAGC;QACf,OAAO,IAAIG,MAAMC,OAAO,CAACF,WAAW;YAClCA,SAASG,IAAI,CAACL;QAChB,OAAO;YACLF,KAAK,CAACC,IAAI,GAAG;gBAACG;gBAAUF;aAAM;QAChC;IACF;IACA,OAAOF;AACT;AAEA,SAASQ,uBAAuBC,KAAc;IAC5C,IAAI,OAAOA,UAAU,UAAU;QAC7B,OAAOA;IACT;IAEA,IACG,OAAOA,UAAU,YAAY,CAACC,MAAMD,UACrC,OAAOA,UAAU,WACjB;QACA,OAAOE,OAAOF;IAChB,OAAO;QACL,OAAO;IACT;AACF;AAEO,SAASX,uBAAuBE,KAAqB;IAC1D,MAAMD,eAAe,IAAIa;IACzB,KAAK,MAAM,CAACX,KAAKC,MAAM,IAAIW,OAAOV,OAAO,CAACH,OAAQ;QAChD,IAAIK,MAAMC,OAAO,CAACJ,QAAQ;YACxB,KAAK,MAAMY,QAAQZ,MAAO;gBACxBH,aAAagB,MAAM,CAACd,KAAKO,uBAAuBM;YAClD;QACF,OAAO;YACLf,aAAaiB,GAAG,CAACf,KAAKO,uBAAuBN;QAC/C;IACF;IACA,OAAOH;AACT;AAEO,SAASH,OACdqB,MAAuB;IACvB,IAAA,IAAA,OAAA,UAAA,MAAA,EAAGC,mBAAH,IAAA,MAAA,OAAA,IAAA,OAAA,IAAA,IAAA,OAAA,GAAA,OAAA,MAAA,OAAA;QAAGA,gBAAAA,CAAH,OAAA,EAAA,GAAA,SAAA,CAAA,KAAsC;;IAEtC,KAAK,MAAMnB,gBAAgBmB,iBAAkB;QAC3C,KAAK,MAAMjB,OAAOF,aAAaoB,IAAI,GAAI;YACrCF,OAAOG,MAAM,CAACnB;QAChB;QAEA,KAAK,MAAM,CAACA,KAAKC,MAAM,IAAIH,aAAaI,OAAO,GAAI;YACjDc,OAAOF,MAAM,CAACd,KAAKC;QACrB;IACF;IAEA,OAAOe;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1906, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/cast-stonev2/cast-stonev2/Frontend/cast-stone-frontend/node_modules/next/src/shared/lib/router/utils/format-url.ts"], "sourcesContent": ["// Format function modified from nodejs\n// Copyright Joyent, Inc. and other Node contributors.\n//\n// Permission is hereby granted, free of charge, to any person obtaining a\n// copy of this software and associated documentation files (the\n// \"Software\"), to deal in the Software without restriction, including\n// without limitation the rights to use, copy, modify, merge, publish,\n// distribute, sublicense, and/or sell copies of the Software, and to permit\n// persons to whom the Software is furnished to do so, subject to the\n// following conditions:\n//\n// The above copyright notice and this permission notice shall be included\n// in all copies or substantial portions of the Software.\n//\n// THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS\n// OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\n// MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN\n// NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM,\n// DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR\n// OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE\n// USE OR OTHER DEALINGS IN THE SOFTWARE.\n\nimport type { UrlObject } from 'url'\nimport type { ParsedUrlQuery } from 'querystring'\nimport * as querystring from './querystring'\n\nconst slashedProtocols = /https?|ftp|gopher|file/\n\nexport function formatUrl(urlObj: UrlObject) {\n  let { auth, hostname } = urlObj\n  let protocol = urlObj.protocol || ''\n  let pathname = urlObj.pathname || ''\n  let hash = urlObj.hash || ''\n  let query = urlObj.query || ''\n  let host: string | false = false\n\n  auth = auth ? encodeURIComponent(auth).replace(/%3A/i, ':') + '@' : ''\n\n  if (urlObj.host) {\n    host = auth + urlObj.host\n  } else if (hostname) {\n    host = auth + (~hostname.indexOf(':') ? `[${hostname}]` : hostname)\n    if (urlObj.port) {\n      host += ':' + urlObj.port\n    }\n  }\n\n  if (query && typeof query === 'object') {\n    query = String(querystring.urlQueryToSearchParams(query as ParsedUrlQuery))\n  }\n\n  let search = urlObj.search || (query && `?${query}`) || ''\n\n  if (protocol && !protocol.endsWith(':')) protocol += ':'\n\n  if (\n    urlObj.slashes ||\n    ((!protocol || slashedProtocols.test(protocol)) && host !== false)\n  ) {\n    host = '//' + (host || '')\n    if (pathname && pathname[0] !== '/') pathname = '/' + pathname\n  } else if (!host) {\n    host = ''\n  }\n\n  if (hash && hash[0] !== '#') hash = '#' + hash\n  if (search && search[0] !== '?') search = '?' + search\n\n  pathname = pathname.replace(/[?#]/g, encodeURIComponent)\n  search = search.replace('#', '%23')\n\n  return `${protocol}${host}${pathname}${search}${hash}`\n}\n\nexport const urlObjectKeys = [\n  'auth',\n  'hash',\n  'host',\n  'hostname',\n  'href',\n  'path',\n  'pathname',\n  'port',\n  'protocol',\n  'query',\n  'search',\n  'slashes',\n]\n\nexport function formatWithValidation(url: UrlObject): string {\n  if (process.env.NODE_ENV === 'development') {\n    if (url !== null && typeof url === 'object') {\n      Object.keys(url).forEach((key) => {\n        if (!urlObjectKeys.includes(key)) {\n          console.warn(\n            `Unknown key passed via urlObject into url.format: ${key}`\n          )\n        }\n      })\n    }\n  }\n\n  return formatUrl(url)\n}\n"], "names": ["formatUrl", "formatWithValidation", "urlObjectKeys", "slashedProtocols", "url<PERSON>bj", "auth", "hostname", "protocol", "pathname", "hash", "query", "host", "encodeURIComponent", "replace", "indexOf", "port", "String", "querystring", "urlQueryToSearchParams", "search", "endsWith", "slashes", "test", "url", "process", "env", "NODE_ENV", "Object", "keys", "for<PERSON>ach", "key", "includes", "console", "warn"], "mappings": "AAAA,uCAAuC;AACvC,sDAAsD;AACtD,EAAE;AACF,0EAA0E;AAC1E,gEAAgE;AAChE,sEAAsE;AACtE,sEAAsE;AACtE,4EAA4E;AAC5E,qEAAqE;AACrE,wBAAwB;AACxB,EAAE;AACF,0EAA0E;AAC1E,yDAAyD;AACzD,EAAE;AACF,0EAA0E;AAC1E,6DAA6D;AAC7D,4EAA4E;AAC5E,2EAA2E;AAC3E,wEAAwE;AACxE,4EAA4E;AAC5E,yCAAyC;AAsEnCwB,QAAQC,GAAG,CAACC,QAAQ,KAAK;;;;;;;;;;;;;;;;;IA9Df1B,SAAS,EAAA;eAATA;;IA6DAC,oBAAoB,EAAA;eAApBA;;IAfHC,aAAa,EAAA;eAAbA;;;;uEAlDgB;AAE7B,MAAMC,mBAAmB;AAElB,SAASH,UAAUI,MAAiB;IACzC,IAAI,EAAEC,IAAI,EAAEC,QAAQ,EAAE,GAAGF;IACzB,IAAIG,WAAWH,OAAOG,QAAQ,IAAI;IAClC,IAAIC,WAAWJ,OAAOI,QAAQ,IAAI;IAClC,IAAIC,OAAOL,OAAOK,IAAI,IAAI;IAC1B,IAAIC,QAAQN,OAAOM,KAAK,IAAI;IAC5B,IAAIC,OAAuB;IAE3BN,OAAOA,OAAOO,mBAAmBP,MAAMQ,OAAO,CAAC,QAAQ,OAAO,MAAM;IAEpE,IAAIT,OAAOO,IAAI,EAAE;QACfA,OAAON,OAAOD,OAAOO,IAAI;IAC3B,OAAO,IAAIL,UAAU;QACnBK,OAAON,OAAQ,CAAA,CAACC,SAASQ,OAAO,CAAC,OAAQ,MAAGR,WAAS,MAAKA,QAAO;QACjE,IAAIF,OAAOW,IAAI,EAAE;YACfJ,QAAQ,MAAMP,OAAOW,IAAI;QAC3B;IACF;IAEA,IAAIL,SAAS,OAAOA,UAAU,UAAU;QACtCA,QAAQM,OAAOC,aAAYC,sBAAsB,CAACR;IACpD;IAEA,IAAIS,SAASf,OAAOe,MAAM,IAAKT,SAAU,MAAGA,SAAY;IAExD,IAAIH,YAAY,CAACA,SAASa,QAAQ,CAAC,MAAMb,YAAY;IAErD,IACEH,OAAOiB,OAAO,IACZ,CAAA,CAACd,YAAYJ,iBAAiBmB,IAAI,CAACf,SAAQ,KAAMI,SAAS,OAC5D;QACAA,OAAO,OAAQA,CAAAA,QAAQ,EAAC;QACxB,IAAIH,YAAYA,QAAQ,CAAC,EAAE,KAAK,KAAKA,WAAW,MAAMA;IACxD,OAAO,IAAI,CAACG,MAAM;QAChBA,OAAO;IACT;IAEA,IAAIF,QAAQA,IAAI,CAAC,EAAE,KAAK,KAAKA,OAAO,MAAMA;IAC1C,IAAIU,UAAUA,MAAM,CAAC,EAAE,KAAK,KAAKA,SAAS,MAAMA;IAEhDX,WAAWA,SAASK,OAAO,CAAC,SAASD;IACrCO,SAASA,OAAON,OAAO,CAAC,KAAK;IAE7B,OAAQ,KAAEN,WAAWI,OAAOH,WAAWW,SAASV;AAClD;AAEO,MAAMP,gBAAgB;IAC3B;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;CACD;AAEM,SAASD,qBAAqBsB,GAAc;IACjD,wCAA4C;QAC1C,IAAIA,QAAQ,QAAQ,OAAOA,QAAQ,UAAU;YAC3CI,OAAOC,IAAI,CAACL,KAAKM,OAAO,CAAC,CAACC;gBACxB,IAAI,CAAC5B,cAAc6B,QAAQ,CAACD,MAAM;oBAChCE,QAAQC,IAAI,CACT,uDAAoDH;gBAEzD;YACF;QACF;IACF;IAEA,OAAO9B,UAAUuB;AACnB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2021, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/cast-stonev2/cast-stonev2/Frontend/cast-stone-frontend/node_modules/next/src/client/use-merged-ref.ts"], "sourcesContent": ["import { useCallback, useRef, type Ref } from 'react'\n\n// This is a compatibility hook to support React 18 and 19 refs.\n// In 19, a cleanup function from refs may be returned.\n// In 18, returning a cleanup function creates a warning.\n// Since we take userspace refs, we don't know ahead of time if a cleanup function will be returned.\n// This implements cleanup functions with the old behavior in 18.\n// We know refs are always called alternating with `null` and then `T`.\n// So a call with `null` means we need to call the previous cleanup functions.\nexport function useMergedRef<TElement>(\n  refA: Ref<TElement>,\n  refB: Ref<TElement>\n): Ref<TElement> {\n  const cleanupA = useRef<(() => void) | null>(null)\n  const cleanupB = useRef<(() => void) | null>(null)\n\n  // NOTE: In theory, we could skip the wrapping if only one of the refs is non-null.\n  // (this happens often if the user doesn't pass a ref to Link/Form/Image)\n  // But this can cause us to leak a cleanup-ref into user code (e.g. via `<Link legacyBehavior>`),\n  // and the user might pass that ref into ref-merging library that doesn't support cleanup refs\n  // (because it hasn't been updated for React 19)\n  // which can then cause things to blow up, because a cleanup-returning ref gets called with `null`.\n  // So in practice, it's safer to be defensive and always wrap the ref, even on React 19.\n  return useCallback(\n    (current: TElement | null): void => {\n      if (current === null) {\n        const cleanupFnA = cleanupA.current\n        if (cleanupFnA) {\n          cleanupA.current = null\n          cleanupFnA()\n        }\n        const cleanupFnB = cleanupB.current\n        if (cleanupFnB) {\n          cleanupB.current = null\n          cleanupFnB()\n        }\n      } else {\n        if (refA) {\n          cleanupA.current = applyRef(refA, current)\n        }\n        if (refB) {\n          cleanupB.current = applyRef(refB, current)\n        }\n      }\n    },\n    [refA, refB]\n  )\n}\n\nfunction applyRef<TElement>(\n  refA: NonNullable<Ref<TElement>>,\n  current: TElement\n) {\n  if (typeof refA === 'function') {\n    const cleanup = refA(current)\n    if (typeof cleanup === 'function') {\n      return cleanup\n    } else {\n      return () => refA(null)\n    }\n  } else {\n    refA.current = current\n    return () => {\n      refA.current = null\n    }\n  }\n}\n"], "names": ["useMergedRef", "refA", "refB", "cleanupA", "useRef", "cleanupB", "useCallback", "current", "cleanupFnA", "cleanupFnB", "applyRef", "cleanup"], "mappings": ";;;;+BASgBA,gBAAAA;;;eAAAA;;;uBAT8B;AASvC,SAASA,aACdC,IAAmB,EACnBC,IAAmB;IAEnB,MAAMC,WAAWC,CAAAA,GAAAA,OAAAA,MAAM,EAAsB;IAC7C,MAAMC,WAAWD,CAAAA,GAAAA,OAAAA,MAAM,EAAsB;IAE7C,mFAAmF;IACnF,yEAAyE;IACzE,iGAAiG;IACjG,8FAA8F;IAC9F,gDAAgD;IAChD,mGAAmG;IACnG,wFAAwF;IACxF,OAAOE,CAAAA,GAAAA,OAAAA,WAAW,EAChB,CAACC;QACC,IAAIA,YAAY,MAAM;YACpB,MAAMC,aAAaL,SAASI,OAAO;YACnC,IAAIC,YAAY;gBACdL,SAASI,OAAO,GAAG;gBACnBC;YACF;YACA,MAAMC,aAAaJ,SAASE,OAAO;YACnC,IAAIE,YAAY;gBACdJ,SAASE,OAAO,GAAG;gBACnBE;YACF;QACF,OAAO;YACL,IAAIR,MAAM;gBACRE,SAASI,OAAO,GAAGG,SAAST,MAAMM;YACpC;YACA,IAAIL,MAAM;gBACRG,SAASE,OAAO,GAAGG,SAASR,MAAMK;YACpC;QACF;IACF,GACA;QAACN;QAAMC;KAAK;AAEhB;AAEA,SAASQ,SACPT,IAAgC,EAChCM,OAAiB;IAEjB,IAAI,OAAON,SAAS,YAAY;QAC9B,MAAMU,UAAUV,KAAKM;QACrB,IAAI,OAAOI,YAAY,YAAY;YACjC,OAAOA;QACT,OAAO;YACL,OAAO,IAAMV,KAAK;QACpB;IACF,OAAO;QACLA,KAAKM,OAAO,GAAGA;QACf,OAAO;YACLN,KAAKM,OAAO,GAAG;QACjB;IACF;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2094, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/cast-stonev2/cast-stonev2/Frontend/cast-stone-frontend/node_modules/next/src/shared/lib/utils.ts"], "sourcesContent": ["import type { HtmlProps } from './html-context.shared-runtime'\nimport type { ComponentType, JSX } from 'react'\nimport type { DomainLocale } from '../../server/config'\nimport type { Env } from '@next/env'\nimport type { IncomingMessage, ServerResponse } from 'http'\nimport type { NextRouter } from './router/router'\nimport type { ParsedUrlQuery } from 'querystring'\nimport type { PreviewData } from '../../types'\nimport type { COMPILER_NAMES } from './constants'\nimport type fs from 'fs'\n\nexport type NextComponentType<\n  Context extends BaseContext = NextPageContext,\n  InitialProps = {},\n  Props = {},\n> = ComponentType<Props> & {\n  /**\n   * Used for initial page load data population. Data returned from `getInitialProps` is serialized when server rendered.\n   * Make sure to return plain `Object` without using `Date`, `Map`, `Set`.\n   * @param context Context of `page`\n   */\n  getInitialProps?(context: Context): InitialProps | Promise<InitialProps>\n}\n\nexport type DocumentType = NextComponentType<\n  DocumentContext,\n  DocumentInitialProps,\n  DocumentProps\n>\n\nexport type AppType<P = {}> = NextComponentType<\n  AppContextType,\n  P,\n  AppPropsType<any, P>\n>\n\nexport type AppTreeType = ComponentType<\n  AppInitialProps & { [name: string]: any }\n>\n\n/**\n * Web vitals provided to _app.reportWebVitals by Core Web Vitals plugin developed by Google Chrome team.\n * https://nextjs.org/blog/next-9-4#integrated-web-vitals-reporting\n */\nexport const WEB_VITALS = ['CLS', 'FCP', 'FID', 'INP', 'LCP', 'TTFB'] as const\nexport type NextWebVitalsMetric = {\n  id: string\n  startTime: number\n  value: number\n  attribution?: { [key: string]: unknown }\n} & (\n  | {\n      label: 'web-vital'\n      name: (typeof WEB_VITALS)[number]\n    }\n  | {\n      label: 'custom'\n      name:\n        | 'Next.js-hydration'\n        | 'Next.js-route-change-to-render'\n        | 'Next.js-render'\n    }\n)\n\nexport type Enhancer<C> = (Component: C) => C\n\nexport type ComponentsEnhancer =\n  | {\n      enhanceApp?: Enhancer<AppType>\n      enhanceComponent?: Enhancer<NextComponentType>\n    }\n  | Enhancer<NextComponentType>\n\nexport type RenderPageResult = {\n  html: string\n  head?: Array<JSX.Element | null>\n}\n\nexport type RenderPage = (\n  options?: ComponentsEnhancer\n) => DocumentInitialProps | Promise<DocumentInitialProps>\n\nexport type BaseContext = {\n  res?: ServerResponse\n  [k: string]: any\n}\n\nexport type NEXT_DATA = {\n  props: Record<string, any>\n  page: string\n  query: ParsedUrlQuery\n  buildId: string\n  assetPrefix?: string\n  runtimeConfig?: { [key: string]: any }\n  nextExport?: boolean\n  autoExport?: boolean\n  isFallback?: boolean\n  isExperimentalCompile?: boolean\n  dynamicIds?: (string | number)[]\n  err?: Error & {\n    statusCode?: number\n    source?: typeof COMPILER_NAMES.server | typeof COMPILER_NAMES.edgeServer\n  }\n  gsp?: boolean\n  gssp?: boolean\n  customServer?: boolean\n  gip?: boolean\n  appGip?: boolean\n  locale?: string\n  locales?: readonly string[]\n  defaultLocale?: string\n  domainLocales?: readonly DomainLocale[]\n  scriptLoader?: any[]\n  isPreview?: boolean\n  notFoundSrcPage?: string\n}\n\n/**\n * `Next` context\n */\nexport interface NextPageContext {\n  /**\n   * Error object if encountered during rendering\n   */\n  err?: (Error & { statusCode?: number }) | null\n  /**\n   * `HTTP` request object.\n   */\n  req?: IncomingMessage\n  /**\n   * `HTTP` response object.\n   */\n  res?: ServerResponse\n  /**\n   * Path section of `URL`.\n   */\n  pathname: string\n  /**\n   * Query string section of `URL` parsed as an object.\n   */\n  query: ParsedUrlQuery\n  /**\n   * `String` of the actual path including query.\n   */\n  asPath?: string\n  /**\n   * The currently active locale\n   */\n  locale?: string\n  /**\n   * All configured locales\n   */\n  locales?: readonly string[]\n  /**\n   * The configured default locale\n   */\n  defaultLocale?: string\n  /**\n   * `Component` the tree of the App to use if needing to render separately\n   */\n  AppTree: AppTreeType\n}\n\nexport type AppContextType<Router extends NextRouter = NextRouter> = {\n  Component: NextComponentType<NextPageContext>\n  AppTree: AppTreeType\n  ctx: NextPageContext\n  router: Router\n}\n\nexport type AppInitialProps<PageProps = any> = {\n  pageProps: PageProps\n}\n\nexport type AppPropsType<\n  Router extends NextRouter = NextRouter,\n  PageProps = {},\n> = AppInitialProps<PageProps> & {\n  Component: NextComponentType<NextPageContext, any, any>\n  router: Router\n  __N_SSG?: boolean\n  __N_SSP?: boolean\n}\n\nexport type DocumentContext = NextPageContext & {\n  renderPage: RenderPage\n  defaultGetInitialProps(\n    ctx: DocumentContext,\n    options?: { nonce?: string }\n  ): Promise<DocumentInitialProps>\n}\n\nexport type DocumentInitialProps = RenderPageResult & {\n  styles?: React.ReactElement[] | Iterable<React.ReactNode> | JSX.Element\n}\n\nexport type DocumentProps = DocumentInitialProps & HtmlProps\n\n/**\n * Next `API` route request\n */\nexport interface NextApiRequest extends IncomingMessage {\n  /**\n   * Object of `query` values from url\n   */\n  query: Partial<{\n    [key: string]: string | string[]\n  }>\n  /**\n   * Object of `cookies` from header\n   */\n  cookies: Partial<{\n    [key: string]: string\n  }>\n\n  body: any\n\n  env: Env\n\n  draftMode?: boolean\n\n  preview?: boolean\n  /**\n   * Preview data set on the request, if any\n   * */\n  previewData?: PreviewData\n}\n\n/**\n * Send body of response\n */\ntype Send<T> = (body: T) => void\n\n/**\n * Next `API` route response\n */\nexport type NextApiResponse<Data = any> = ServerResponse & {\n  /**\n   * Send data `any` data in response\n   */\n  send: Send<Data>\n  /**\n   * Send data `json` data in response\n   */\n  json: Send<Data>\n  status: (statusCode: number) => NextApiResponse<Data>\n  redirect(url: string): NextApiResponse<Data>\n  redirect(status: number, url: string): NextApiResponse<Data>\n\n  /**\n   * Set draft mode\n   */\n  setDraftMode: (options: { enable: boolean }) => NextApiResponse<Data>\n\n  /**\n   * Set preview data for Next.js' prerender mode\n   */\n  setPreviewData: (\n    data: object | string,\n    options?: {\n      /**\n       * Specifies the number (in seconds) for the preview session to last for.\n       * The given number will be converted to an integer by rounding down.\n       * By default, no maximum age is set and the preview session finishes\n       * when the client shuts down (browser is closed).\n       */\n      maxAge?: number\n      /**\n       * Specifies the path for the preview session to work under. By default,\n       * the path is considered the \"default path\", i.e., any pages under \"/\".\n       */\n      path?: string\n    }\n  ) => NextApiResponse<Data>\n\n  /**\n   * Clear preview data for Next.js' prerender mode\n   */\n  clearPreviewData: (options?: { path?: string }) => NextApiResponse<Data>\n\n  /**\n   * Revalidate a specific page and regenerate it using On-Demand Incremental\n   * Static Regeneration.\n   * The path should be an actual path, not a rewritten path. E.g. for\n   * \"/blog/[slug]\" this should be \"/blog/post-1\".\n   * @link https://nextjs.org/docs/app/building-your-application/data-fetching/incremental-static-regeneration#on-demand-revalidation-with-revalidatepath\n   */\n  revalidate: (\n    urlPath: string,\n    opts?: {\n      unstable_onlyGenerated?: boolean\n    }\n  ) => Promise<void>\n}\n\n/**\n * Next `API` route handler\n */\nexport type NextApiHandler<T = any> = (\n  req: NextApiRequest,\n  res: NextApiResponse<T>\n) => unknown | Promise<unknown>\n\n/**\n * Utils\n */\nexport function execOnce<T extends (...args: any[]) => ReturnType<T>>(\n  fn: T\n): T {\n  let used = false\n  let result: ReturnType<T>\n\n  return ((...args: any[]) => {\n    if (!used) {\n      used = true\n      result = fn(...args)\n    }\n    return result\n  }) as T\n}\n\n// Scheme: https://tools.ietf.org/html/rfc3986#section-3.1\n// Absolute URL: https://tools.ietf.org/html/rfc3986#section-4.3\nconst ABSOLUTE_URL_REGEX = /^[a-zA-Z][a-zA-Z\\d+\\-.]*?:/\nexport const isAbsoluteUrl = (url: string) => ABSOLUTE_URL_REGEX.test(url)\n\nexport function getLocationOrigin() {\n  const { protocol, hostname, port } = window.location\n  return `${protocol}//${hostname}${port ? ':' + port : ''}`\n}\n\nexport function getURL() {\n  const { href } = window.location\n  const origin = getLocationOrigin()\n  return href.substring(origin.length)\n}\n\nexport function getDisplayName<P>(Component: ComponentType<P>) {\n  return typeof Component === 'string'\n    ? Component\n    : Component.displayName || Component.name || 'Unknown'\n}\n\nexport function isResSent(res: ServerResponse) {\n  return res.finished || res.headersSent\n}\n\nexport function normalizeRepeatedSlashes(url: string) {\n  const urlParts = url.split('?')\n  const urlNoQuery = urlParts[0]\n\n  return (\n    urlNoQuery\n      // first we replace any non-encoded backslashes with forward\n      // then normalize repeated forward slashes\n      .replace(/\\\\/g, '/')\n      .replace(/\\/\\/+/g, '/') +\n    (urlParts[1] ? `?${urlParts.slice(1).join('?')}` : '')\n  )\n}\n\nexport async function loadGetInitialProps<\n  C extends BaseContext,\n  IP = {},\n  P = {},\n>(App: NextComponentType<C, IP, P>, ctx: C): Promise<IP> {\n  if (process.env.NODE_ENV !== 'production') {\n    if (App.prototype?.getInitialProps) {\n      const message = `\"${getDisplayName(\n        App\n      )}.getInitialProps()\" is defined as an instance method - visit https://nextjs.org/docs/messages/get-initial-props-as-an-instance-method for more information.`\n      throw new Error(message)\n    }\n  }\n  // when called from _app `ctx` is nested in `ctx`\n  const res = ctx.res || (ctx.ctx && ctx.ctx.res)\n\n  if (!App.getInitialProps) {\n    if (ctx.ctx && ctx.Component) {\n      // @ts-ignore pageProps default\n      return {\n        pageProps: await loadGetInitialProps(ctx.Component, ctx.ctx),\n      }\n    }\n    return {} as IP\n  }\n\n  const props = await App.getInitialProps(ctx)\n\n  if (res && isResSent(res)) {\n    return props\n  }\n\n  if (!props) {\n    const message = `\"${getDisplayName(\n      App\n    )}.getInitialProps()\" should resolve to an object. But found \"${props}\" instead.`\n    throw new Error(message)\n  }\n\n  if (process.env.NODE_ENV !== 'production') {\n    if (Object.keys(props).length === 0 && !ctx.ctx) {\n      console.warn(\n        `${getDisplayName(\n          App\n        )} returned an empty object from \\`getInitialProps\\`. This de-optimizes and prevents automatic static optimization. https://nextjs.org/docs/messages/empty-object-getInitialProps`\n      )\n    }\n  }\n\n  return props\n}\n\nexport const SP = typeof performance !== 'undefined'\nexport const ST =\n  SP &&\n  (['mark', 'measure', 'getEntriesByName'] as const).every(\n    (method) => typeof performance[method] === 'function'\n  )\n\nexport class DecodeError extends Error {}\nexport class NormalizeError extends Error {}\nexport class PageNotFoundError extends Error {\n  code: string\n\n  constructor(page: string) {\n    super()\n    this.code = 'ENOENT'\n    this.name = 'PageNotFoundError'\n    this.message = `Cannot find module for page: ${page}`\n  }\n}\n\nexport class MissingStaticPage extends Error {\n  constructor(page: string, message: string) {\n    super()\n    this.message = `Failed to load static file for page: ${page} ${message}`\n  }\n}\n\nexport class MiddlewareNotFoundError extends Error {\n  code: string\n  constructor() {\n    super()\n    this.code = 'ENOENT'\n    this.message = `Cannot find the middleware module`\n  }\n}\n\nexport interface CacheFs {\n  existsSync: typeof fs.existsSync\n  readFile: typeof fs.promises.readFile\n  readFileSync: typeof fs.readFileSync\n  writeFile(f: string, d: any): Promise<void>\n  mkdir(dir: string): Promise<void | string>\n  stat(f: string): Promise<{ mtime: Date }>\n}\n\nexport function stringifyError(error: Error) {\n  return JSON.stringify({ message: error.message, stack: error.stack })\n}\n"], "names": ["DecodeError", "MiddlewareNotFoundError", "MissingStaticPage", "NormalizeError", "PageNotFoundError", "SP", "ST", "WEB_VITALS", "execOnce", "getDisplayName", "getLocationOrigin", "getURL", "isAbsoluteUrl", "isResSent", "loadGetInitialProps", "normalizeRepeatedSlashes", "stringifyError", "fn", "used", "result", "args", "ABSOLUTE_URL_REGEX", "url", "test", "protocol", "hostname", "port", "window", "location", "href", "origin", "substring", "length", "Component", "displayName", "name", "res", "finished", "headersSent", "urlParts", "split", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "replace", "slice", "join", "App", "ctx", "process", "env", "NODE_ENV", "prototype", "getInitialProps", "message", "Error", "pageProps", "props", "Object", "keys", "console", "warn", "performance", "every", "method", "constructor", "page", "code", "error", "JSON", "stringify", "stack"], "mappings": "AA8WM+C,QAAQC,GAAG,CAACC,QAAQ,KAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAsDlBjD,WAAW,EAAA;eAAXA;;IAoBAC,uBAAuB,EAAA;eAAvBA;;IAPAC,iBAAiB,EAAA;eAAjBA;;IAZAC,cAAc,EAAA;eAAdA;;IACAC,iBAAiB,EAAA;eAAjBA;;IATAC,EAAE,EAAA;eAAFA;;IACAC,EAAE,EAAA;eAAFA;;IAlXAC,UAAU,EAAA;eAAVA;;IAsQGC,QAAQ,EAAA;eAARA;;IA+BAC,cAAc,EAAA;eAAdA;;IAXAC,iBAAiB,EAAA;eAAjBA;;IAKAC,MAAM,EAAA;eAANA;;IAPHC,aAAa,EAAA;eAAbA;;IAmBGC,SAAS,EAAA;eAATA;;IAkBMC,mBAAmB,EAAA;eAAnBA;;IAdNC,wBAAwB,EAAA;eAAxBA;;IA+GAC,cAAc,EAAA;eAAdA;;;AA9ZT,MAAMT,aAAa;IAAC;IAAO;IAAO;IAAO;IAAO;IAAO;CAAO;AAsQ9D,SAASC,SACdS,EAAK;IAEL,IAAIC,OAAO;IACX,IAAIC;IAEJ,OAAQ;yCAAIC,OAAAA,IAAAA,MAAAA,OAAAA,OAAAA,GAAAA,OAAAA,MAAAA,OAAAA;YAAAA,IAAAA,CAAAA,KAAAA,GAAAA,SAAAA,CAAAA,KAAAA;;QACV,IAAI,CAACF,MAAM;YACTA,OAAO;YACPC,SAASF,MAAMG;QACjB;QACA,OAAOD;IACT;AACF;AAEA,0DAA0D;AAC1D,gEAAgE;AAChE,MAAME,qBAAqB;AACpB,MAAMT,gBAAgB,CAACU,MAAgBD,mBAAmBE,IAAI,CAACD;AAE/D,SAASZ;IACd,MAAM,EAAEc,QAAQ,EAAEC,QAAQ,EAAEC,IAAI,EAAE,GAAGC,OAAOC,QAAQ;IACpD,OAAUJ,WAAS,OAAIC,WAAWC,CAAAA,OAAO,MAAMA,OAAO,EAAC;AACzD;AAEO,SAASf;IACd,MAAM,EAAEkB,IAAI,EAAE,GAAGF,OAAOC,QAAQ;IAChC,MAAME,SAASpB;IACf,OAAOmB,KAAKE,SAAS,CAACD,OAAOE,MAAM;AACrC;AAEO,SAASvB,eAAkBwB,SAA2B;IAC3D,OAAO,OAAOA,cAAc,WACxBA,YACAA,UAAUC,WAAW,IAAID,UAAUE,IAAI,IAAI;AACjD;AAEO,SAAStB,UAAUuB,GAAmB;IAC3C,OAAOA,IAAIC,QAAQ,IAAID,IAAIE,WAAW;AACxC;AAEO,SAASvB,yBAAyBO,GAAW;IAClD,MAAMiB,WAAWjB,IAAIkB,KAAK,CAAC;IAC3B,MAAMC,aAAaF,QAAQ,CAAC,EAAE;IAE9B,OACEE,WACE,4DAA4D;IAC5D,0CAA0C;KACzCC,OAAO,CAAC,OAAO,KACfA,OAAO,CAAC,UAAU,OACpBH,CAAAA,QAAQ,CAAC,EAAE,GAAI,MAAGA,SAASI,KAAK,CAAC,GAAGC,IAAI,CAAC,OAAS,EAAC;AAExD;AAEO,eAAe9B,oBAIpB+B,GAAgC,EAAEC,GAAM;IACxC,wCAA2C;YACrCD;QAAJ,IAAA,CAAIA,iBAAAA,IAAIK,SAAS,KAAA,OAAA,KAAA,IAAbL,eAAeM,eAAe,EAAE;YAClC,MAAMC,UAAW,MAAG3C,eAClBoC,OACA;YACF,MAAM,OAAA,cAAkB,CAAlB,IAAIQ,MAAMD,UAAV,qBAAA;uBAAA;4BAAA;8BAAA;YAAiB;QACzB;IACF;IACA,iDAAiD;IACjD,MAAMhB,MAAMU,IAAIV,GAAG,IAAKU,IAAIA,GAAG,IAAIA,IAAIA,GAAG,CAACV,GAAG;IAE9C,IAAI,CAACS,IAAIM,eAAe,EAAE;QACxB,IAAIL,IAAIA,GAAG,IAAIA,IAAIb,SAAS,EAAE;YAC5B,+BAA+B;YAC/B,OAAO;gBACLqB,WAAW,MAAMxC,oBAAoBgC,IAAIb,SAAS,EAAEa,IAAIA,GAAG;YAC7D;QACF;QACA,OAAO,CAAC;IACV;IAEA,MAAMS,QAAQ,MAAMV,IAAIM,eAAe,CAACL;IAExC,IAAIV,OAAOvB,UAAUuB,MAAM;QACzB,OAAOmB;IACT;IAEA,IAAI,CAACA,OAAO;QACV,MAAMH,UAAW,MAAG3C,eAClBoC,OACA,iEAA8DU,QAAM;QACtE,MAAM,OAAA,cAAkB,CAAlB,IAAIF,MAAMD,UAAV,qBAAA;mBAAA;wBAAA;0BAAA;QAAiB;IACzB;IAEA,IAAIL,QAAQC,GAAG,CAACC,QAAQ,KAAK,WAAc;QACzC,IAAIO,OAAOC,IAAI,CAACF,OAAOvB,MAAM,KAAK,KAAK,CAACc,IAAIA,GAAG,EAAE;YAC/CY,QAAQC,IAAI,CACT,KAAElD,eACDoC,OACA;QAEN;IACF;IAEA,OAAOU;AACT;AAEO,MAAMlD,KAAK,OAAOuD,gBAAgB;AAClC,MAAMtD,KACXD,MACC;IAAC;IAAQ;IAAW;CAAmB,CAAWwD,KAAK,CACtD,CAACC,SAAW,OAAOF,WAAW,CAACE,OAAO,KAAK;AAGxC,MAAM9D,oBAAoBqD;AAAO;AACjC,MAAMlD,uBAAuBkD;AAAO;AACpC,MAAMjD,0BAA0BiD;IAGrCU,YAAYC,IAAY,CAAE;QACxB,KAAK;QACL,IAAI,CAACC,IAAI,GAAG;QACZ,IAAI,CAAC9B,IAAI,GAAG;QACZ,IAAI,CAACiB,OAAO,GAAI,kCAA+BY;IACjD;AACF;AAEO,MAAM9D,0BAA0BmD;IACrCU,YAAYC,IAAY,EAAEZ,OAAe,CAAE;QACzC,KAAK;QACL,IAAI,CAACA,OAAO,GAAI,0CAAuCY,OAAK,MAAGZ;IACjE;AACF;AAEO,MAAMnD,gCAAgCoD;IAE3CU,aAAc;QACZ,KAAK;QACL,IAAI,CAACE,IAAI,GAAG;QACZ,IAAI,CAACb,OAAO,GAAI;IAClB;AACF;AAWO,SAASpC,eAAekD,KAAY;IACzC,OAAOC,KAAKC,SAAS,CAAC;QAAEhB,SAASc,MAAMd,OAAO;QAAEiB,OAAOH,MAAMG,KAAK;IAAC;AACrE", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2309, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/cast-stonev2/cast-stonev2/Frontend/cast-stone-frontend/node_modules/next/src/shared/lib/router/utils/is-local-url.ts"], "sourcesContent": ["import { isAbsoluteUrl, getLocationOrigin } from '../../utils'\nimport { hasBasePath } from '../../../../client/has-base-path'\n\n/**\n * Detects whether a given url is routable by the Next.js router (browser only).\n */\nexport function isLocalURL(url: string): boolean {\n  // prevent a hydration mismatch on href for url with anchor refs\n  if (!isAbsoluteUrl(url)) return true\n  try {\n    // absolute urls can be local if they are on the same origin\n    const locationOrigin = getLocationOrigin()\n    const resolved = new URL(url, locationOrigin)\n    return resolved.origin === locationOrigin && hasBasePath(resolved.pathname)\n  } catch (_) {\n    return false\n  }\n}\n"], "names": ["isLocalURL", "url", "isAbsoluteUrl", "locationOrigin", "getLocationOrigin", "resolved", "URL", "origin", "has<PERSON>ase<PERSON><PERSON>", "pathname", "_"], "mappings": ";;;;+BAMgBA,cAAAA;;;eAAAA;;;uBANiC;6BACrB;AAKrB,SAASA,WAAWC,GAAW;IACpC,gEAAgE;IAChE,IAAI,CAACC,CAAAA,GAAAA,OAAAA,aAAa,EAACD,MAAM,OAAO;IAChC,IAAI;QACF,4DAA4D;QAC5D,MAAME,iBAAiBC,CAAAA,GAAAA,OAAAA,iBAAiB;QACxC,MAAMC,WAAW,IAAIC,IAAIL,KAAKE;QAC9B,OAAOE,SAASE,MAAM,KAAKJ,kBAAkBK,CAAAA,GAAAA,aAAAA,WAAW,EAACH,SAASI,QAAQ;IAC5E,EAAE,OAAOC,GAAG;QACV,OAAO;IACT;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2338, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/cast-stonev2/cast-stonev2/Frontend/cast-stone-frontend/node_modules/next/src/shared/lib/utils/error-once.ts"], "sourcesContent": ["let errorOnce = (_: string) => {}\nif (process.env.NODE_ENV !== 'production') {\n  const errors = new Set<string>()\n  errorOnce = (msg: string) => {\n    if (!errors.has(msg)) {\n      console.error(msg)\n    }\n    errors.add(msg)\n  }\n}\n\nexport { errorOnce }\n"], "names": ["errorOnce", "_", "process", "env", "NODE_ENV", "errors", "Set", "msg", "has", "console", "error", "add"], "mappings": "AACIE,QAAQC,GAAG,CAACC,QAAQ,KAAK;;;;;+BAUpBJ,aAAAA;;;eAAAA;;;AAXT,IAAIA,YAAY,CAACC,KAAe;AAChC,wCAA2C;IACzC,MAAMI,SAAS,IAAIC;IACnBN,YAAY,CAACO;QACX,IAAI,CAACF,OAAOG,GAAG,CAACD,MAAM;YACpBE,QAAQC,KAAK,CAACH;QAChB;QACAF,OAAOM,GAAG,CAACJ;IACb;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2364, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/cast-stonev2/cast-stonev2/Frontend/cast-stone-frontend/node_modules/next/src/client/app-dir/link.tsx"], "sourcesContent": ["'use client'\n\nimport React, { createContext, useContext, useOptimistic, useRef } from 'react'\nimport type { UrlObject } from 'url'\nimport { formatUrl } from '../../shared/lib/router/utils/format-url'\nimport { AppRouterContext } from '../../shared/lib/app-router-context.shared-runtime'\nimport { PrefetchKind } from '../components/router-reducer/router-reducer-types'\nimport { useMergedRef } from '../use-merged-ref'\nimport { isAbsoluteUrl } from '../../shared/lib/utils'\nimport { addBasePath } from '../add-base-path'\nimport { warnOnce } from '../../shared/lib/utils/warn-once'\nimport type { PENDING_LINK_STATUS } from '../components/links'\nimport {\n  IDLE_LINK_STATUS,\n  mountLinkInstance,\n  onNavigationIntent,\n  unmountLinkForCurrentNavigation,\n  unmountPrefetchableInstance,\n  type LinkInstance,\n} from '../components/links'\nimport { isLocalURL } from '../../shared/lib/router/utils/is-local-url'\nimport { dispatchNavigateAction } from '../components/app-router-instance'\nimport { errorOnce } from '../../shared/lib/utils/error-once'\n\ntype Url = string | UrlObject\ntype RequiredKeys<T> = {\n  [K in keyof T]-?: {} extends Pick<T, K> ? never : K\n}[keyof T]\ntype OptionalKeys<T> = {\n  [K in keyof T]-?: {} extends Pick<T, K> ? K : never\n}[keyof T]\n\ntype OnNavigateEventHandler = (event: { preventDefault: () => void }) => void\n\ntype InternalLinkProps = {\n  /**\n   * **Required**. The path or URL to navigate to. It can also be an object (similar to `URL`).\n   *\n   * @example\n   * ```tsx\n   * // Navigate to /dashboard:\n   * <Link href=\"/dashboard\">Dashboard</Link>\n   *\n   * // Navigate to /about?name=test:\n   * <Link href={{ pathname: '/about', query: { name: 'test' } }}>\n   *   About\n   * </Link>\n   * ```\n   *\n   * @remarks\n   * - For external URLs, use a fully qualified URL such as `https://...`.\n   * - In the App Router, dynamic routes must not include bracketed segments in `href`.\n   */\n  href: Url\n\n  /**\n   * @deprecated v10.0.0: `href` props pointing to a dynamic route are\n   * automatically resolved and no longer require the `as` prop.\n   */\n  as?: Url\n\n  /**\n   * Replace the current `history` state instead of adding a new URL into the stack.\n   *\n   * @defaultValue `false`\n   *\n   * @example\n   * ```tsx\n   * <Link href=\"/about\" replace>\n   *   About (replaces the history state)\n   * </Link>\n   * ```\n   */\n  replace?: boolean\n\n  /**\n   * Whether to override the default scroll behavior. If `true`, Next.js attempts to maintain\n   * the scroll position if the newly navigated page is still visible. If not, it scrolls to the top.\n   *\n   * If `false`, Next.js will not modify the scroll behavior at all.\n   *\n   * @defaultValue `true`\n   *\n   * @example\n   * ```tsx\n   * <Link href=\"/dashboard\" scroll={false}>\n   *   No auto scroll\n   * </Link>\n   * ```\n   */\n  scroll?: boolean\n\n  /**\n   * Update the path of the current page without rerunning data fetching methods\n   * like `getStaticProps`, `getServerSideProps`, or `getInitialProps`.\n   *\n   * @remarks\n   * `shallow` only applies to the Pages Router. For the App Router, see the\n   * [following documentation](https://nextjs.org/docs/app/building-your-application/routing/linking-and-navigating#using-the-native-history-api).\n   *\n   * @defaultValue `false`\n   *\n   * @example\n   * ```tsx\n   * <Link href=\"/blog\" shallow>\n   *   Shallow navigation\n   * </Link>\n   * ```\n   */\n  shallow?: boolean\n\n  /**\n   * Forces `Link` to pass its `href` to the child component. Useful if the child is a custom\n   * component that wraps an `<a>` tag, or if you're using certain styling libraries.\n   *\n   * @defaultValue `false`\n   *\n   * @example\n   * ```tsx\n   * <Link href=\"/dashboard\" passHref>\n   *   <MyStyledAnchor>Dashboard</MyStyledAnchor>\n   * </Link>\n   * ```\n   */\n  passHref?: boolean\n\n  /**\n   * Prefetch the page in the background.\n   * Any `<Link />` that is in the viewport (initially or through scroll) will be prefetched.\n   * Prefetch can be disabled by passing `prefetch={false}`.\n   *\n   * @remarks\n   * Prefetching is only enabled in production.\n   *\n   * - In the **App Router**:\n   *   - `null` (default): Prefetch behavior depends on static vs dynamic routes:\n   *     - Static routes: fully prefetched\n   *     - Dynamic routes: partial prefetch to the nearest segment with a `loading.js`\n   *   - `true`: Always prefetch the full route and data.\n   *   - `false`: Disable prefetching on both viewport and hover.\n   * - In the **Pages Router**:\n   *   - `true` (default): Prefetches the route and data in the background on viewport or hover.\n   *   - `false`: Prefetch only on hover, not on viewport.\n   *\n   * @defaultValue `true` (Pages Router) or `null` (App Router)\n   *\n   * @example\n   * ```tsx\n   * <Link href=\"/dashboard\" prefetch={false}>\n   *   Dashboard\n   * </Link>\n   * ```\n   */\n  prefetch?: boolean | null\n\n  /**\n   * (unstable) Switch to a dynamic prefetch on hover. Effectively the same as\n   * updating the prefetch prop to `true` in a mouse event.\n   */\n  unstable_dynamicOnHover?: boolean\n\n  /**\n   * The active locale is automatically prepended in the Pages Router. `locale` allows for providing\n   * a different locale, or can be set to `false` to opt out of automatic locale behavior.\n   *\n   * @remarks\n   * Note: locale only applies in the Pages Router and is ignored in the App Router.\n   *\n   * @example\n   * ```tsx\n   * // Use the 'fr' locale:\n   * <Link href=\"/about\" locale=\"fr\">\n   *   About (French)\n   * </Link>\n   *\n   * // Disable locale prefix:\n   * <Link href=\"/about\" locale={false}>\n   *   About (no locale prefix)\n   * </Link>\n   * ```\n   */\n  locale?: string | false\n\n  /**\n   * Enable legacy link behavior, requiring an `<a>` tag to wrap the child content\n   * if the child is a string or number.\n   *\n   * @deprecated This will be removed in v16\n   * @defaultValue `false`\n   * @see https://github.com/vercel/next.js/commit/489e65ed98544e69b0afd7e0cfc3f9f6c2b803b7\n   */\n  legacyBehavior?: boolean\n\n  /**\n   * Optional event handler for when the mouse pointer is moved onto the `<Link>`.\n   */\n  onMouseEnter?: React.MouseEventHandler<HTMLAnchorElement>\n\n  /**\n   * Optional event handler for when the `<Link>` is touched.\n   */\n  onTouchStart?: React.TouchEventHandler<HTMLAnchorElement>\n\n  /**\n   * Optional event handler for when the `<Link>` is clicked.\n   */\n  onClick?: React.MouseEventHandler<HTMLAnchorElement>\n\n  /**\n   * Optional event handler for when the `<Link>` is navigated.\n   */\n  onNavigate?: OnNavigateEventHandler\n}\n\n// TODO-APP: Include the full set of Anchor props\n// adding this to the publicly exported type currently breaks existing apps\n\n// `RouteInferType` is a stub here to avoid breaking `typedRoutes` when the type\n// isn't generated yet. It will be replaced when the webpack plugin runs.\n// eslint-disable-next-line @typescript-eslint/no-unused-vars\nexport type LinkProps<RouteInferType = any> = InternalLinkProps\ntype LinkPropsRequired = RequiredKeys<LinkProps>\ntype LinkPropsOptional = OptionalKeys<Omit<InternalLinkProps, 'locale'>>\n\nfunction isModifiedEvent(event: React.MouseEvent): boolean {\n  const eventTarget = event.currentTarget as HTMLAnchorElement | SVGAElement\n  const target = eventTarget.getAttribute('target')\n  return (\n    (target && target !== '_self') ||\n    event.metaKey ||\n    event.ctrlKey ||\n    event.shiftKey ||\n    event.altKey || // triggers resource download\n    (event.nativeEvent && event.nativeEvent.which === 2)\n  )\n}\n\nfunction linkClicked(\n  e: React.MouseEvent,\n  href: string,\n  as: string,\n  linkInstanceRef: React.RefObject<LinkInstance | null>,\n  replace?: boolean,\n  scroll?: boolean,\n  onNavigate?: OnNavigateEventHandler\n): void {\n  const { nodeName } = e.currentTarget\n\n  // anchors inside an svg have a lowercase nodeName\n  const isAnchorNodeName = nodeName.toUpperCase() === 'A'\n\n  if (\n    (isAnchorNodeName && isModifiedEvent(e)) ||\n    e.currentTarget.hasAttribute('download')\n  ) {\n    // ignore click for browser’s default behavior\n    return\n  }\n\n  if (!isLocalURL(href)) {\n    if (replace) {\n      // browser default behavior does not replace the history state\n      // so we need to do it manually\n      e.preventDefault()\n      location.replace(href)\n    }\n\n    // ignore click for browser’s default behavior\n    return\n  }\n\n  e.preventDefault()\n\n  const navigate = () => {\n    if (onNavigate) {\n      let isDefaultPrevented = false\n\n      onNavigate({\n        preventDefault: () => {\n          isDefaultPrevented = true\n        },\n      })\n\n      if (isDefaultPrevented) {\n        return\n      }\n    }\n\n    dispatchNavigateAction(\n      as || href,\n      replace ? 'replace' : 'push',\n      scroll ?? true,\n      linkInstanceRef.current\n    )\n  }\n\n  React.startTransition(navigate)\n}\n\nfunction formatStringOrUrl(urlObjOrString: UrlObject | string): string {\n  if (typeof urlObjOrString === 'string') {\n    return urlObjOrString\n  }\n\n  return formatUrl(urlObjOrString)\n}\n\n/**\n * A React component that extends the HTML `<a>` element to provide\n * [prefetching](https://nextjs.org/docs/app/building-your-application/routing/linking-and-navigating#2-prefetching)\n * and client-side navigation. This is the primary way to navigate between routes in Next.js.\n *\n * @remarks\n * - Prefetching is only enabled in production.\n *\n * @see https://nextjs.org/docs/app/api-reference/components/link\n */\nexport default function LinkComponent(\n  props: LinkProps & {\n    children: React.ReactNode\n    ref: React.Ref<HTMLAnchorElement>\n  }\n) {\n  const [linkStatus, setOptimisticLinkStatus] = useOptimistic(IDLE_LINK_STATUS)\n\n  let children: React.ReactNode\n\n  const linkInstanceRef = useRef<LinkInstance | null>(null)\n\n  const {\n    href: hrefProp,\n    as: asProp,\n    children: childrenProp,\n    prefetch: prefetchProp = null,\n    passHref,\n    replace,\n    shallow,\n    scroll,\n    onClick,\n    onMouseEnter: onMouseEnterProp,\n    onTouchStart: onTouchStartProp,\n    legacyBehavior = false,\n    onNavigate,\n    ref: forwardedRef,\n    unstable_dynamicOnHover,\n    ...restProps\n  } = props\n\n  children = childrenProp\n\n  if (\n    legacyBehavior &&\n    (typeof children === 'string' || typeof children === 'number')\n  ) {\n    children = <a>{children}</a>\n  }\n\n  const router = React.useContext(AppRouterContext)\n\n  const prefetchEnabled = prefetchProp !== false\n  /**\n   * The possible states for prefetch are:\n   * - null: this is the default \"auto\" mode, where we will prefetch partially if the link is in the viewport\n   * - true: we will prefetch if the link is visible and prefetch the full page, not just partially\n   * - false: we will not prefetch if in the viewport at all\n   * - 'unstable_dynamicOnHover': this starts in \"auto\" mode, but switches to \"full\" when the link is hovered\n   */\n  const appPrefetchKind =\n    prefetchProp === null ? PrefetchKind.AUTO : PrefetchKind.FULL\n\n  if (process.env.NODE_ENV !== 'production') {\n    function createPropError(args: {\n      key: string\n      expected: string\n      actual: string\n    }) {\n      return new Error(\n        `Failed prop type: The prop \\`${args.key}\\` expects a ${args.expected} in \\`<Link>\\`, but got \\`${args.actual}\\` instead.` +\n          (typeof window !== 'undefined'\n            ? \"\\nOpen your browser's console to view the Component stack trace.\"\n            : '')\n      )\n    }\n\n    // TypeScript trick for type-guarding:\n    const requiredPropsGuard: Record<LinkPropsRequired, true> = {\n      href: true,\n    } as const\n    const requiredProps: LinkPropsRequired[] = Object.keys(\n      requiredPropsGuard\n    ) as LinkPropsRequired[]\n    requiredProps.forEach((key: LinkPropsRequired) => {\n      if (key === 'href') {\n        if (\n          props[key] == null ||\n          (typeof props[key] !== 'string' && typeof props[key] !== 'object')\n        ) {\n          throw createPropError({\n            key,\n            expected: '`string` or `object`',\n            actual: props[key] === null ? 'null' : typeof props[key],\n          })\n        }\n      } else {\n        // TypeScript trick for type-guarding:\n        // eslint-disable-next-line @typescript-eslint/no-unused-vars\n        const _: never = key\n      }\n    })\n\n    // TypeScript trick for type-guarding:\n    const optionalPropsGuard: Record<LinkPropsOptional, true> = {\n      as: true,\n      replace: true,\n      scroll: true,\n      shallow: true,\n      passHref: true,\n      prefetch: true,\n      unstable_dynamicOnHover: true,\n      onClick: true,\n      onMouseEnter: true,\n      onTouchStart: true,\n      legacyBehavior: true,\n      onNavigate: true,\n    } as const\n    const optionalProps: LinkPropsOptional[] = Object.keys(\n      optionalPropsGuard\n    ) as LinkPropsOptional[]\n    optionalProps.forEach((key: LinkPropsOptional) => {\n      const valType = typeof props[key]\n\n      if (key === 'as') {\n        if (props[key] && valType !== 'string' && valType !== 'object') {\n          throw createPropError({\n            key,\n            expected: '`string` or `object`',\n            actual: valType,\n          })\n        }\n      } else if (\n        key === 'onClick' ||\n        key === 'onMouseEnter' ||\n        key === 'onTouchStart' ||\n        key === 'onNavigate'\n      ) {\n        if (props[key] && valType !== 'function') {\n          throw createPropError({\n            key,\n            expected: '`function`',\n            actual: valType,\n          })\n        }\n      } else if (\n        key === 'replace' ||\n        key === 'scroll' ||\n        key === 'shallow' ||\n        key === 'passHref' ||\n        key === 'prefetch' ||\n        key === 'legacyBehavior' ||\n        key === 'unstable_dynamicOnHover'\n      ) {\n        if (props[key] != null && valType !== 'boolean') {\n          throw createPropError({\n            key,\n            expected: '`boolean`',\n            actual: valType,\n          })\n        }\n      } else {\n        // TypeScript trick for type-guarding:\n        // eslint-disable-next-line @typescript-eslint/no-unused-vars\n        const _: never = key\n      }\n    })\n  }\n\n  if (process.env.NODE_ENV !== 'production') {\n    if (props.locale) {\n      warnOnce(\n        'The `locale` prop is not supported in `next/link` while using the `app` router. Read more about app router internalization: https://nextjs.org/docs/app/building-your-application/routing/internationalization'\n      )\n    }\n    if (!asProp) {\n      let href: string | undefined\n      if (typeof hrefProp === 'string') {\n        href = hrefProp\n      } else if (\n        typeof hrefProp === 'object' &&\n        typeof hrefProp.pathname === 'string'\n      ) {\n        href = hrefProp.pathname\n      }\n\n      if (href) {\n        const hasDynamicSegment = href\n          .split('/')\n          .some((segment) => segment.startsWith('[') && segment.endsWith(']'))\n\n        if (hasDynamicSegment) {\n          throw new Error(\n            `Dynamic href \\`${href}\\` found in <Link> while using the \\`/app\\` router, this is not supported. Read more: https://nextjs.org/docs/messages/app-dir-dynamic-href`\n          )\n        }\n      }\n    }\n  }\n\n  const { href, as } = React.useMemo(() => {\n    const resolvedHref = formatStringOrUrl(hrefProp)\n    return {\n      href: resolvedHref,\n      as: asProp ? formatStringOrUrl(asProp) : resolvedHref,\n    }\n  }, [hrefProp, asProp])\n\n  // This will return the first child, if multiple are provided it will throw an error\n  let child: any\n  if (legacyBehavior) {\n    if (process.env.NODE_ENV === 'development') {\n      if (onClick) {\n        console.warn(\n          `\"onClick\" was passed to <Link> with \\`href\\` of \\`${hrefProp}\\` but \"legacyBehavior\" was set. The legacy behavior requires onClick be set on the child of next/link`\n        )\n      }\n      if (onMouseEnterProp) {\n        console.warn(\n          `\"onMouseEnter\" was passed to <Link> with \\`href\\` of \\`${hrefProp}\\` but \"legacyBehavior\" was set. The legacy behavior requires onMouseEnter be set on the child of next/link`\n        )\n      }\n      try {\n        child = React.Children.only(children)\n      } catch (err) {\n        if (!children) {\n          throw new Error(\n            `No children were passed to <Link> with \\`href\\` of \\`${hrefProp}\\` but one child is required https://nextjs.org/docs/messages/link-no-children`\n          )\n        }\n        throw new Error(\n          `Multiple children were passed to <Link> with \\`href\\` of \\`${hrefProp}\\` but only one child is supported https://nextjs.org/docs/messages/link-multiple-children` +\n            (typeof window !== 'undefined'\n              ? \" \\nOpen your browser's console to view the Component stack trace.\"\n              : '')\n        )\n      }\n    } else {\n      child = React.Children.only(children)\n    }\n  } else {\n    if (process.env.NODE_ENV === 'development') {\n      if ((children as any)?.type === 'a') {\n        throw new Error(\n          'Invalid <Link> with <a> child. Please remove <a> or use <Link legacyBehavior>.\\nLearn more: https://nextjs.org/docs/messages/invalid-new-link-with-extra-anchor'\n        )\n      }\n    }\n  }\n\n  const childRef: any = legacyBehavior\n    ? child && typeof child === 'object' && child.ref\n    : forwardedRef\n\n  // Use a callback ref to attach an IntersectionObserver to the anchor tag on\n  // mount. In the future we will also use this to keep track of all the\n  // currently mounted <Link> instances, e.g. so we can re-prefetch them after\n  // a revalidation or refresh.\n  const observeLinkVisibilityOnMount = React.useCallback(\n    (element: HTMLAnchorElement | SVGAElement) => {\n      if (router !== null) {\n        linkInstanceRef.current = mountLinkInstance(\n          element,\n          href,\n          router,\n          appPrefetchKind,\n          prefetchEnabled,\n          setOptimisticLinkStatus\n        )\n      }\n\n      return () => {\n        if (linkInstanceRef.current) {\n          unmountLinkForCurrentNavigation(linkInstanceRef.current)\n          linkInstanceRef.current = null\n        }\n        unmountPrefetchableInstance(element)\n      }\n    },\n    [prefetchEnabled, href, router, appPrefetchKind, setOptimisticLinkStatus]\n  )\n\n  const mergedRef = useMergedRef(observeLinkVisibilityOnMount, childRef)\n\n  const childProps: {\n    onTouchStart?: React.TouchEventHandler<HTMLAnchorElement>\n    onMouseEnter: React.MouseEventHandler<HTMLAnchorElement>\n    onClick: React.MouseEventHandler<HTMLAnchorElement>\n    href?: string\n    ref?: any\n  } = {\n    ref: mergedRef,\n    onClick(e) {\n      if (process.env.NODE_ENV !== 'production') {\n        if (!e) {\n          throw new Error(\n            `Component rendered inside next/link has to pass click event to \"onClick\" prop.`\n          )\n        }\n      }\n\n      if (!legacyBehavior && typeof onClick === 'function') {\n        onClick(e)\n      }\n\n      if (\n        legacyBehavior &&\n        child.props &&\n        typeof child.props.onClick === 'function'\n      ) {\n        child.props.onClick(e)\n      }\n\n      if (!router) {\n        return\n      }\n\n      if (e.defaultPrevented) {\n        return\n      }\n\n      linkClicked(e, href, as, linkInstanceRef, replace, scroll, onNavigate)\n    },\n    onMouseEnter(e) {\n      if (!legacyBehavior && typeof onMouseEnterProp === 'function') {\n        onMouseEnterProp(e)\n      }\n\n      if (\n        legacyBehavior &&\n        child.props &&\n        typeof child.props.onMouseEnter === 'function'\n      ) {\n        child.props.onMouseEnter(e)\n      }\n\n      if (!router) {\n        return\n      }\n\n      if (!prefetchEnabled || process.env.NODE_ENV === 'development') {\n        return\n      }\n\n      const upgradeToDynamicPrefetch = unstable_dynamicOnHover === true\n      onNavigationIntent(\n        e.currentTarget as HTMLAnchorElement | SVGAElement,\n        upgradeToDynamicPrefetch\n      )\n    },\n    onTouchStart: process.env.__NEXT_LINK_NO_TOUCH_START\n      ? undefined\n      : function onTouchStart(e) {\n          if (!legacyBehavior && typeof onTouchStartProp === 'function') {\n            onTouchStartProp(e)\n          }\n\n          if (\n            legacyBehavior &&\n            child.props &&\n            typeof child.props.onTouchStart === 'function'\n          ) {\n            child.props.onTouchStart(e)\n          }\n\n          if (!router) {\n            return\n          }\n\n          if (!prefetchEnabled) {\n            return\n          }\n\n          const upgradeToDynamicPrefetch = unstable_dynamicOnHover === true\n          onNavigationIntent(\n            e.currentTarget as HTMLAnchorElement | SVGAElement,\n            upgradeToDynamicPrefetch\n          )\n        },\n  }\n\n  // If child is an <a> tag and doesn't have a href attribute, or if the 'passHref' property is\n  // defined, we specify the current 'href', so that repetition is not needed by the user.\n  // If the url is absolute, we can bypass the logic to prepend the basePath.\n  if (isAbsoluteUrl(as)) {\n    childProps.href = as\n  } else if (\n    !legacyBehavior ||\n    passHref ||\n    (child.type === 'a' && !('href' in child.props))\n  ) {\n    childProps.href = addBasePath(as)\n  }\n\n  let link: React.ReactNode\n\n  if (legacyBehavior) {\n    if (process.env.NODE_ENV === 'development') {\n      errorOnce(\n        '`legacyBehavior` is deprecated and will be removed in a future ' +\n          'release. A codemod is available to upgrade your components:\\n\\n' +\n          'npx @next/codemod@latest new-link .\\n\\n' +\n          'Learn more: https://nextjs.org/docs/app/building-your-application/upgrading/codemods#remove-a-tags-from-link-components'\n      )\n    }\n    link = React.cloneElement(child, childProps)\n  } else {\n    link = (\n      <a {...restProps} {...childProps}>\n        {children}\n      </a>\n    )\n  }\n\n  return (\n    <LinkStatusContext.Provider value={linkStatus}>\n      {link}\n    </LinkStatusContext.Provider>\n  )\n}\n\nconst LinkStatusContext = createContext<\n  typeof PENDING_LINK_STATUS | typeof IDLE_LINK_STATUS\n>(IDLE_LINK_STATUS)\n\nexport const useLinkStatus = () => {\n  return useContext(LinkStatusContext)\n}\n"], "names": ["LinkComponent", "useLinkStatus", "isModifiedEvent", "event", "eventTarget", "currentTarget", "target", "getAttribute", "metaKey", "ctrl<PERSON>ey", "shift<PERSON>ey", "altKey", "nativeEvent", "which", "linkClicked", "e", "href", "as", "linkInstanceRef", "replace", "scroll", "onNavigate", "nodeName", "isAnchorNodeName", "toUpperCase", "hasAttribute", "isLocalURL", "preventDefault", "location", "navigate", "isDefaultPrevented", "dispatchNavigateAction", "current", "React", "startTransition", "formatStringOrUrl", "urlObjOrString", "formatUrl", "props", "linkStatus", "setOptimisticLinkStatus", "useOptimistic", "IDLE_LINK_STATUS", "children", "useRef", "hrefProp", "asProp", "childrenProp", "prefetch", "prefetchProp", "passHref", "shallow", "onClick", "onMouseEnter", "onMouseEnterProp", "onTouchStart", "onTouchStartProp", "legacyBeh<PERSON>or", "ref", "forwardedRef", "unstable_dynamicOnHover", "restProps", "a", "router", "useContext", "AppRouterContext", "prefetchEnabled", "appPrefetchKind", "PrefetchKind", "AUTO", "FULL", "process", "env", "NODE_ENV", "createPropError", "args", "Error", "key", "expected", "actual", "window", "requiredPropsGuard", "requiredProps", "Object", "keys", "for<PERSON>ach", "_", "optionalPropsGuard", "optionalProps", "valType", "locale", "warnOnce", "pathname", "hasDynamicSegment", "split", "some", "segment", "startsWith", "endsWith", "useMemo", "resolvedHref", "child", "console", "warn", "Children", "only", "err", "type", "childRef", "observeLinkVisibilityOnMount", "useCallback", "element", "mountLinkInstance", "unmountLinkForCurrentNavigation", "unmountPrefetchableInstance", "mergedRef", "useMergedRef", "childProps", "defaultPrevented", "upgradeToDynamicPrefetch", "onNavigationIntent", "__NEXT_LINK_NO_TOUCH_START", "undefined", "isAbsoluteUrl", "addBasePath", "link", "errorOnce", "cloneElement", "LinkStatusContext", "Provider", "value", "createContext"], "mappings": "AAkXMuE,QAAQC,GAAG,CAACC,QAAQ,KAAK;AAlX/B;;;;;;;;;;;;;;;;IAmTA;;;;;;;;;CASC,GACD,OAyZC,EAAA;eAzZuBzE;;IA+ZXC,aAAa,EAAA;eAAbA;;;;;iEA1tB2D;2BAE9C;+CACO;oCACJ;8BACA;uBACC;6BACF;0BACH;uBASlB;4BACoB;mCACY;2BACb;AA0M1B,SAASC,gBAAgBC,KAAuB;IAC9C,MAAMC,cAAcD,MAAME,aAAa;IACvC,MAAMC,SAASF,YAAYG,YAAY,CAAC;IACxC,OACGD,UAAUA,WAAW,WACtBH,MAAMK,OAAO,IACbL,MAAMM,OAAO,IACbN,MAAMO,QAAQ,IACdP,MAAMQ,MAAM,IAAI,6BAA6B;IAC5CR,MAAMS,WAAW,IAAIT,MAAMS,WAAW,CAACC,KAAK,KAAK;AAEtD;AAEA,SAASC,YACPC,CAAmB,EACnBC,IAAY,EACZC,EAAU,EACVC,eAAqD,EACrDC,OAAiB,EACjBC,MAAgB,EAChBC,UAAmC;IAEnC,MAAM,EAAEC,QAAQ,EAAE,GAAGP,EAAEV,aAAa;IAEpC,kDAAkD;IAClD,MAAMkB,mBAAmBD,SAASE,WAAW,OAAO;IAEpD,IACGD,oBAAoBrB,gBAAgBa,MACrCA,EAAEV,aAAa,CAACoB,YAAY,CAAC,aAC7B;QACA,8CAA8C;QAC9C;IACF;IAEA,IAAI,CAACC,CAAAA,GAAAA,YAAAA,UAAU,EAACV,OAAO;QACrB,IAAIG,SAAS;YACX,8DAA8D;YAC9D,+BAA+B;YAC/BJ,EAAEY,cAAc;YAChBC,SAAST,OAAO,CAACH;QACnB;QAEA,8CAA8C;QAC9C;IACF;IAEAD,EAAEY,cAAc;IAEhB,MAAME,WAAW;QACf,IAAIR,YAAY;YACd,IAAIS,qBAAqB;YAEzBT,WAAW;gBACTM,gBAAgB;oBACdG,qBAAqB;gBACvB;YACF;YAEA,IAAIA,oBAAoB;gBACtB;YACF;QACF;QAEAC,CAAAA,GAAAA,mBAAAA,sBAAsB,EACpBd,MAAMD,MACNG,UAAU,YAAY,QACtBC,UAAAA,OAAAA,SAAU,MACVF,gBAAgBc,OAAO;IAE3B;IAEAC,OAAAA,OAAK,CAACC,eAAe,CAACL;AACxB;AAEA,SAASM,kBAAkBC,cAAkC;IAC3D,IAAI,OAAOA,mBAAmB,UAAU;QACtC,OAAOA;IACT;IAEA,OAAOC,CAAAA,GAAAA,WAAAA,SAAS,EAACD;AACnB;AAYe,SAASpC,cACtBsC,KAGC;IAED,MAAM,CAACC,YAAYC,wBAAwB,GAAGC,CAAAA,GAAAA,OAAAA,aAAa,EAACC,OAAAA,gBAAgB;IAE5E,IAAIC;IAEJ,MAAMzB,kBAAkB0B,CAAAA,GAAAA,OAAAA,MAAM,EAAsB;IAEpD,MAAM,EACJ5B,MAAM6B,QAAQ,EACd5B,IAAI6B,MAAM,EACVH,UAAUI,YAAY,EACtBC,UAAUC,eAAe,IAAI,EAC7BC,QAAQ,EACR/B,OAAO,EACPgC,OAAO,EACP/B,MAAM,EACNgC,OAAO,EACPC,cAAcC,gBAAgB,EAC9BC,cAAcC,gBAAgB,EAC9BC,iBAAiB,KAAK,EACtBpC,UAAU,EACVqC,KAAKC,YAAY,EACjBC,uBAAuB,EACvB,GAAGC,WACJ,GAAGvB;IAEJK,WAAWI;IAEX,IACEU,kBACC,CAAA,OAAOd,aAAa,YAAY,OAAOA,aAAa,QAAO,GAC5D;QACAA,WAAAA,WAAAA,GAAW,CAAA,GAAA,YAAA,GAAA,EAACmB,KAAAA;sBAAGnB;;IACjB;IAEA,MAAMoB,SAAS9B,OAAAA,OAAK,CAAC+B,UAAU,CAACC,+BAAAA,gBAAgB;IAEhD,MAAMC,kBAAkBjB,iBAAiB;IACzC;;;;;;GAMC,GACD,MAAMkB,kBACJlB,iBAAiB,OAAOmB,oBAAAA,YAAY,CAACC,IAAI,GAAGD,oBAAAA,YAAY,CAACE,IAAI;IAE/D,wCAA2C;QACzC,SAASI,gBAAgBC,IAIxB;YACC,OAAO,OAAA,cAKN,CALM,IAAIC,MACR,iCAA+BD,KAAKE,GAAG,GAAC,iBAAeF,KAAKG,QAAQ,GAAC,4BAA4BH,KAAKI,MAAM,GAAC,eAC3G,CAAA,OAAOC,WAAW,cACf,qEACA,EAAC,IAJF,qBAAA;uBAAA;4BAAA;8BAAA;YAKP;QACF;QAEA,sCAAsC;QACtC,MAAMC,qBAAsD;YAC1DjE,MAAM;QACR;QACA,MAAMkE,gBAAqCC,OAAOC,IAAI,CACpDH;QAEFC,cAAcG,OAAO,CAAC,CAACR;YACrB,IAAIA,QAAQ,QAAQ;gBAClB,IACEvC,KAAK,CAACuC,IAAI,IAAI,QACb,OAAOvC,KAAK,CAACuC,IAAI,KAAK,YAAY,OAAOvC,KAAK,CAACuC,IAAI,KAAK,UACzD;oBACA,MAAMH,gBAAgB;wBACpBG;wBACAC,UAAU;wBACVC,QAAQzC,KAAK,CAACuC,IAAI,KAAK,OAAO,SAAS,OAAOvC,KAAK,CAACuC,IAAI;oBAC1D;gBACF;YACF,OAAO;gBACL,sCAAsC;gBACtC,6DAA6D;gBAC7D,MAAMS,IAAWT;YACnB;QACF;QAEA,sCAAsC;QACtC,MAAMU,qBAAsD;YAC1DtE,IAAI;YACJE,SAAS;YACTC,QAAQ;YACR+B,SAAS;YACTD,UAAU;YACVF,UAAU;YACVY,yBAAyB;YACzBR,SAAS;YACTC,cAAc;YACdE,cAAc;YACdE,gBAAgB;YAChBpC,YAAY;QACd;QACA,MAAMmE,gBAAqCL,OAAOC,IAAI,CACpDG;QAEFC,cAAcH,OAAO,CAAC,CAACR;YACrB,MAAMY,UAAU,OAAOnD,KAAK,CAACuC,IAAI;YAEjC,IAAIA,QAAQ,MAAM;gBAChB,IAAIvC,KAAK,CAACuC,IAAI,IAAIY,YAAY,YAAYA,YAAY,UAAU;oBAC9D,MAAMf,gBAAgB;wBACpBG;wBACAC,UAAU;wBACVC,QAAQU;oBACV;gBACF;YACF,OAAO,IACLZ,QAAQ,aACRA,QAAQ,kBACRA,QAAQ,kBACRA,QAAQ,cACR;gBACA,IAAIvC,KAAK,CAACuC,IAAI,IAAIY,YAAY,YAAY;oBACxC,MAAMf,gBAAgB;wBACpBG;wBACAC,UAAU;wBACVC,QAAQU;oBACV;gBACF;YACF,OAAO,IACLZ,QAAQ,aACRA,QAAQ,YACRA,QAAQ,aACRA,QAAQ,cACRA,QAAQ,cACRA,QAAQ,oBACRA,QAAQ,2BACR;gBACA,IAAIvC,KAAK,CAACuC,IAAI,IAAI,QAAQY,YAAY,WAAW;oBAC/C,MAAMf,gBAAgB;wBACpBG;wBACAC,UAAU;wBACVC,QAAQU;oBACV;gBACF;YACF,OAAO;gBACL,sCAAsC;gBACtC,6DAA6D;gBAC7D,MAAMH,IAAWT;YACnB;QACF;IACF;IAEA,IAAIN,QAAQC,GAAG,CAACC,QAAQ,KAAK,WAAc;QACzC,IAAInC,MAAMoD,MAAM,EAAE;YAChBC,CAAAA,GAAAA,UAAAA,QAAQ,EACN;QAEJ;QACA,IAAI,CAAC7C,QAAQ;YACX,IAAI9B;YACJ,IAAI,OAAO6B,aAAa,UAAU;gBAChC7B,OAAO6B;YACT,OAAO,IACL,OAAOA,aAAa,YACpB,OAAOA,SAAS+C,QAAQ,KAAK,UAC7B;gBACA5E,OAAO6B,SAAS+C,QAAQ;YAC1B;YAEA,IAAI5E,MAAM;gBACR,MAAM6E,oBAAoB7E,KACvB8E,KAAK,CAAC,KACNC,IAAI,CAAC,CAACC,UAAYA,QAAQC,UAAU,CAAC,QAAQD,QAAQE,QAAQ,CAAC;gBAEjE,IAAIL,mBAAmB;oBACrB,MAAM,OAAA,cAEL,CAFK,IAAIjB,MACP,mBAAiB5D,OAAK,6IADnB,qBAAA;+BAAA;oCAAA;sCAAA;oBAEN;gBACF;YACF;QACF;IACF;IAEA,MAAM,EAAEA,IAAI,EAAEC,EAAE,EAAE,GAAGgB,OAAAA,OAAK,CAACkE,OAAO;iCAAC;YACjC,MAAMC,eAAejE,kBAAkBU;YACvC,OAAO;gBACL7B,MAAMoF;gBACNnF,IAAI6B,SAASX,kBAAkBW,UAAUsD;YAC3C;QACF;gCAAG;QAACvD;QAAUC;KAAO;IAErB,oFAAoF;IACpF,IAAIuD;IACJ,IAAI5C,gBAAgB;QAClB,IAAIc,QAAQC,GAAG,CAACC,QAAQ,KAAK,WAAe;YAC1C,IAAIrB,SAAS;gBACXkD,QAAQC,IAAI,CACT,oDAAoD1D,WAAS;YAElE;YACA,IAAIS,kBAAkB;gBACpBgD,QAAQC,IAAI,CACT,yDAAyD1D,WAAS;YAEvE;YACA,IAAI;gBACFwD,QAAQpE,OAAAA,OAAK,CAACuE,QAAQ,CAACC,IAAI,CAAC9D;YAC9B,EAAE,OAAO+D,KAAK;gBACZ,IAAI,CAAC/D,UAAU;oBACb,MAAM,OAAA,cAEL,CAFK,IAAIiC,MACP,uDAAuD/B,WAAS,kFAD7D,qBAAA;+BAAA;oCAAA;sCAAA;oBAEN;gBACF;gBACA,MAAM,OAAA,cAKL,CALK,IAAI+B,MACP,6DAA6D/B,WAAS,8FACpE,CAAA,OAAOmC,WAAW,cACf,sEACA,EAAC,IAJH,qBAAA;2BAAA;gCAAA;kCAAA;gBAKN;YACF;QACF,OAAO;;QAEP;IACF,OAAO;QACL,IAAIT,QAAQC,GAAG,CAACC,QAAQ,KAAK,WAAe;YAC1C,IAAI,CAAC9B,YAAAA,OAAAA,KAAAA,IAAAA,SAAkBgE,IAAI,MAAK,KAAK;gBACnC,MAAM,OAAA,cAEL,CAFK,IAAI/B,MACR,oKADI,qBAAA;2BAAA;gCAAA;kCAAA;gBAEN;YACF;QACF;IACF;IAEA,MAAMgC,WAAgBnD,iBAClB4C,SAAS,OAAOA,UAAU,YAAYA,MAAM3C,GAAG,GAC/CC;IAEJ,4EAA4E;IAC5E,sEAAsE;IACtE,4EAA4E;IAC5E,6BAA6B;IAC7B,MAAMkD,+BAA+B5E,OAAAA,OAAK,CAAC6E,WAAW;mEACpD,CAACC;YACC,IAAIhD,WAAW,MAAM;gBACnB7C,gBAAgBc,OAAO,GAAGgF,CAAAA,GAAAA,OAAAA,iBAAiB,EACzCD,SACA/F,MACA+C,QACAI,iBACAD,iBACA1B;YAEJ;YAEA;2EAAO;oBACL,IAAItB,gBAAgBc,OAAO,EAAE;wBAC3BiF,CAAAA,GAAAA,OAAAA,+BAA+B,EAAC/F,gBAAgBc,OAAO;wBACvDd,gBAAgBc,OAAO,GAAG;oBAC5B;oBACAkF,CAAAA,GAAAA,OAAAA,2BAA2B,EAACH;gBAC9B;;QACF;kEACA;QAAC7C;QAAiBlD;QAAM+C;QAAQI;QAAiB3B;KAAwB;IAG3E,MAAM2E,YAAYC,CAAAA,GAAAA,cAAAA,YAAY,EAACP,8BAA8BD;IAE7D,MAAMS,aAMF;QACF3D,KAAKyD;QACL/D,SAAQrC,CAAC;YACP,IAAIwD,QAAQC,GAAG,CAACC,QAAQ,KAAK,WAAc;gBACzC,IAAI,CAAC1D,GAAG;oBACN,MAAM,OAAA,cAEL,CAFK,IAAI6D,MACP,mFADG,qBAAA;+BAAA;oCAAA;sCAAA;oBAEN;gBACF;YACF;YAEA,IAAI,CAACnB,kBAAkB,OAAOL,YAAY,YAAY;gBACpDA,QAAQrC;YACV;YAEA,IACE0C,kBACA4C,MAAM/D,KAAK,IACX,OAAO+D,MAAM/D,KAAK,CAACc,OAAO,KAAK,YAC/B;gBACAiD,MAAM/D,KAAK,CAACc,OAAO,CAACrC;YACtB;YAEA,IAAI,CAACgD,QAAQ;gBACX;YACF;YAEA,IAAIhD,EAAEuG,gBAAgB,EAAE;gBACtB;YACF;YAEAxG,YAAYC,GAAGC,MAAMC,IAAIC,iBAAiBC,SAASC,QAAQC;QAC7D;QACAgC,cAAatC,CAAC;YACZ,IAAI,CAAC0C,kBAAkB,OAAOH,qBAAqB,YAAY;gBAC7DA,iBAAiBvC;YACnB;YAEA,IACE0C,kBACA4C,MAAM/D,KAAK,IACX,OAAO+D,MAAM/D,KAAK,CAACe,YAAY,KAAK,YACpC;gBACAgD,MAAM/D,KAAK,CAACe,YAAY,CAACtC;YAC3B;YAEA,IAAI,CAACgD,QAAQ;gBACX;YACF;YAEA,IAAI,CAACG,mBAAmBK,QAAQC,GAAG,CAACC,IAA4B,IAApB,KAAK;gBAC/C;YACF;;YAEA,MAAM8C,2BAA2B3D,4BAA4B;QAK/D;QACAL,cAAcgB,QAAQC,GAAG,CAACiD,0BAA0B,GAChDC,oCACA,SAASnE,aAAaxC,CAAC;YACrB,IAAI,CAAC0C,kBAAkB,OAAOD,qBAAqB,YAAY;gBAC7DA,iBAAiBzC;YACnB;YAEA,IACE0C,kBACA4C,MAAM/D,KAAK,IACX,OAAO+D,MAAM/D,KAAK,CAACiB,YAAY,KAAK,YACpC;gBACA8C,MAAM/D,KAAK,CAACiB,YAAY,CAACxC;YAC3B;YAEA,IAAI,CAACgD,QAAQ;gBACX;YACF;YAEA,IAAI,CAACG,iBAAiB;gBACpB;YACF;YAEA,MAAMqD,2BAA2B3D,4BAA4B;YAC7D4D,CAAAA,GAAAA,OAAAA,kBAAkB,EAChBzG,EAAEV,aAAa,EACfkH;QAEJ;IACN;IAEA,6FAA6F;IAC7F,wFAAwF;IACxF,2EAA2E;IAC3E,IAAII,CAAAA,GAAAA,OAAAA,aAAa,EAAC1G,KAAK;QACrBoG,WAAWrG,IAAI,GAAGC;IACpB,OAAO,IACL,CAACwC,kBACDP,YACCmD,MAAMM,IAAI,KAAK,OAAO,CAAE,CAAA,UAAUN,MAAM/D,KAAI,GAC7C;QACA+E,WAAWrG,IAAI,GAAG4G,CAAAA,GAAAA,aAAAA,WAAW,EAAC3G;IAChC;IAEA,IAAI4G;IAEJ,IAAIpE,gBAAgB;QAClB,IAAIc,QAAQC,GAAG,CAACC,QAAQ,KAAK,WAAe;YAC1CqD,CAAAA,GAAAA,WAAAA,SAAS,EACP,oEACE,oEACA,4CACA;QAEN;QACAD,OAAAA,WAAAA,GAAO5F,OAAAA,OAAK,CAAC8F,YAAY,CAAC1B,OAAOgB;IACnC,OAAO;QACLQ,OAAAA,WAAAA,GACE,CAAA,GAAA,YAAA,GAAA,EAAC/D,KAAAA;YAAG,GAAGD,SAAS;YAAG,GAAGwD,UAAU;sBAC7B1E;;IAGP;IAEA,OAAA,WAAA,GACE,CAAA,GAAA,YAAA,GAAA,EAACqF,kBAAkBC,QAAQ,EAAA;QAACC,OAAO3F;kBAChCsF;;AAGP;AAEA,MAAMG,oBAAAA,WAAAA,GAAoBG,CAAAA,GAAAA,OAAAA,aAAa,EAErCzF,OAAAA,gBAAgB;AAEX,MAAMzC,gBAAgB;IAC3B,OAAO+D,CAAAA,GAAAA,OAAAA,UAAU,EAACgE;AACpB", "ignoreList": [0], "debugId": null}}]}