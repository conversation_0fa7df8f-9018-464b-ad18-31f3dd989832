module.exports = {

"[externals]/next/dist/compiled/next-server/app-page-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-page-turbo.runtime.dev.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-async-storage.external.js [external] (next/dist/server/app-render/work-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-async-storage.external.js", () => require("next/dist/server/app-render/work-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/action-async-storage.external.js [external] (next/dist/server/app-render/action-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/action-async-storage.external.js", () => require("next/dist/server/app-render/action-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-unit-async-storage.external.js [external] (next/dist/server/app-render/work-unit-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-unit-async-storage.external.js", () => require("next/dist/server/app-render/work-unit-async-storage.external.js"));

module.exports = mod;
}}),
"[project]/src/services/config/apiConfig.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
// API Configuration
__turbopack_context__.s({
    "ApiEndpoints": (()=>ApiEndpoints),
    "ApiError": (()=>ApiError),
    "BaseApiUrl": (()=>BaseApiUrl),
    "HttpMethod": (()=>HttpMethod),
    "buildQueryString": (()=>buildQueryString),
    "defaultHeaders": (()=>defaultHeaders)
});
const BaseApiUrl = ("TURBOPACK compile-time value", "https://localhost:7069/api") || 'https://localhost:7069/api';
var HttpMethod = /*#__PURE__*/ function(HttpMethod) {
    HttpMethod["GET"] = "GET";
    HttpMethod["POST"] = "POST";
    HttpMethod["PUT"] = "PUT";
    HttpMethod["PATCH"] = "PATCH";
    HttpMethod["DELETE"] = "DELETE";
    return HttpMethod;
}({});
const ApiEndpoints = {
    // Collections
    Collections: {
        Base: '/collections',
        ById: (id)=>`/collections/${id}`,
        ByLevel: (level)=>`/collections/level/${level}`,
        Children: (id)=>`/collections/${id}/children`,
        Hierarchy: '/collections/hierarchy',
        Published: '/collections/published',
        Search: '/collections/search',
        Filter: '/collections/filter',
        RefreshRelationships: '/collections/refresh-relationships'
    },
    // Products
    Products: {
        Base: '/products',
        ById: (id)=>`/products/${id}`,
        ByCollection: (collectionId)=>`/products/collection/${collectionId}`,
        InStock: '/products/in-stock',
        Featured: '/products/featured',
        Latest: '/products/latest',
        Search: '/products/search',
        PriceRange: '/products/price-range',
        UpdateStock: (id)=>`/products/${id}/stock`,
        Filter: '/products/filter'
    },
    // Orders
    Orders: {
        Base: '/orders',
        ById: (id)=>`/orders/${id}`,
        ByUser: (userId)=>`/orders/user/${userId}`,
        ByEmail: (email)=>`/orders/email/${email}`,
        ByStatus: (statusId)=>`/orders/status/${statusId}`,
        UpdateStatus: (id)=>`/orders/${id}/status`,
        Cancel: (id)=>`/orders/${id}/cancel`,
        Pending: '/orders/pending',
        Recent: '/orders/recent',
        Details: (id)=>`/orders/${id}/details`,
        Revenue: {
            Total: '/orders/revenue/total',
            Range: '/orders/revenue/range'
        },
        Filter: '/orders/filter'
    },
    // Users
    Users: {
        Base: '/users',
        ById: (id)=>`/users/${id}`,
        ByEmail: (email)=>`/users/email/${email}`,
        ByRole: (role)=>`/users/role/${role}`,
        Active: '/users/active',
        Recent: '/users/recent',
        Deactivate: (id)=>`/users/${id}/deactivate`,
        Activate: (id)=>`/users/${id}/activate`,
        WithOrders: (id)=>`/users/${id}/orders`,
        EmailExists: (email)=>`/users/email-exists/${email}`,
        Filter: '/users/filter'
    },
    // Seeding
    Seed: {
        All: '/seed/all',
        Statuses: '/seed/statuses',
        AdminUser: '/seed/admin-user',
        Collections: '/seed/collections',
        Products: '/seed/products'
    }
};
class ApiError extends Error {
    status;
    errors;
    constructor(message, status, errors){
        super(message), this.status = status, this.errors = errors;
        this.name = 'ApiError';
    }
}
const buildQueryString = (params)=>{
    const searchParams = new URLSearchParams();
    Object.entries(params).forEach(([key, value])=>{
        if (value !== undefined && value !== null && value !== '') {
            if (Array.isArray(value)) {
                value.forEach((item)=>searchParams.append(key, String(item)));
            } else {
                searchParams.append(key, String(value));
            }
        }
    });
    const queryString = searchParams.toString();
    return queryString ? `?${queryString}` : '';
};
const defaultHeaders = {
    'Content-Type': 'application/json',
    'Accept': 'application/json'
};
}}),
"[project]/src/services/config/httpClient.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "HttpClient": (()=>HttpClient),
    "httpClient": (()=>httpClient)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$config$2f$apiConfig$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/services/config/apiConfig.ts [app-ssr] (ecmascript)");
;
class HttpClient {
    baseUrl;
    constructor(baseUrl = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$config$2f$apiConfig$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["BaseApiUrl"]){
        this.baseUrl = baseUrl;
    }
    async request(endpoint, method = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$config$2f$apiConfig$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["HttpMethod"].GET, config = {}) {
        const { params, ...requestConfig } = config;
        // Build URL with query parameters for GET requests
        let url = `${this.baseUrl}${endpoint}`;
        if (method === __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$config$2f$apiConfig$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["HttpMethod"].GET && params) {
            url += (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$config$2f$apiConfig$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["buildQueryString"])(params);
        }
        const requestOptions = {
            method,
            headers: {
                ...__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$config$2f$apiConfig$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["defaultHeaders"],
                ...requestConfig.headers
            },
            ...requestConfig
        };
        // Add body for non-GET requests
        if (method !== __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$config$2f$apiConfig$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["HttpMethod"].GET && requestConfig.body) {
            if (typeof requestConfig.body === 'object') {
                requestOptions.body = JSON.stringify(requestConfig.body);
            }
        }
        try {
            const response = await fetch(url, requestOptions);
            // Handle different response types
            let responseData;
            const contentType = response.headers.get('content-type');
            if (contentType && contentType.includes('application/json')) {
                responseData = await response.json();
            } else {
                // Handle non-JSON responses
                const text = await response.text();
                responseData = {
                    success: response.ok,
                    message: response.ok ? 'Success' : 'Error',
                    data: text
                };
            }
            if (!response.ok) {
                throw new __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$config$2f$apiConfig$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ApiError"](responseData.message || `HTTP error! status: ${response.status}`, response.status, responseData.errors);
            }
            return responseData;
        } catch (error) {
            if (error instanceof __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$config$2f$apiConfig$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ApiError"]) {
                throw error;
            }
            console.error('API request failed:', error);
            throw new __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$config$2f$apiConfig$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ApiError"](error instanceof Error ? error.message : 'Unknown error occurred');
        }
    }
    // GET request
    async get(endpoint, params) {
        return this.request(endpoint, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$config$2f$apiConfig$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["HttpMethod"].GET, {
            params
        });
    }
    // POST request
    async post(endpoint, data, config) {
        return this.request(endpoint, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$config$2f$apiConfig$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["HttpMethod"].POST, {
            ...config,
            body: data
        });
    }
    // PUT request
    async put(endpoint, data, config) {
        return this.request(endpoint, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$config$2f$apiConfig$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["HttpMethod"].PUT, {
            ...config,
            body: data
        });
    }
    // PATCH request
    async patch(endpoint, data, config) {
        return this.request(endpoint, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$config$2f$apiConfig$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["HttpMethod"].PATCH, {
            ...config,
            body: data
        });
    }
    // DELETE request
    async delete(endpoint, config) {
        return this.request(endpoint, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$config$2f$apiConfig$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["HttpMethod"].DELETE, config);
    }
    // Upload file (multipart/form-data)
    async upload(endpoint, formData, config) {
        const uploadConfig = {
            ...config,
            headers: {
                // Don't set Content-Type for FormData, let browser set it with boundary
                ...config?.headers
            },
            body: formData
        };
        // Remove Content-Type header for file uploads
        if (uploadConfig.headers && 'Content-Type' in uploadConfig.headers) {
            delete uploadConfig.headers['Content-Type'];
        }
        return this.request(endpoint, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$config$2f$apiConfig$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["HttpMethod"].POST, uploadConfig);
    }
}
const httpClient = new HttpClient();
;
}}),
"[project]/src/services/config/baseService.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "BaseService": (()=>BaseService),
    "ServiceUtils": (()=>ServiceUtils)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$config$2f$httpClient$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/services/config/httpClient.ts [app-ssr] (ecmascript)");
;
class BaseService {
    client = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$config$2f$httpClient$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["httpClient"];
    /**
   * Handle API response and extract data
   */ async handleResponse(apiCall) {
        try {
            const response = await apiCall;
            if (response.success && response.data !== undefined) {
                return response.data;
            }
            throw new Error(response.message || 'API call failed');
        } catch (error) {
            console.error('API Error:', error);
            throw error;
        }
    }
    /**
   * Handle paginated API response
   */ async handlePaginatedResponse(apiCall) {
        try {
            const response = await apiCall;
            if (response.success && response.data !== undefined) {
                return response.data;
            }
            throw new Error(response.message || 'API call failed');
        } catch (error) {
            console.error('API Error:', error);
            throw error;
        }
    }
    /**
   * Handle API response without data extraction (for operations like delete)
   */ async handleVoidResponse(apiCall) {
        try {
            const response = await apiCall;
            return response.success;
        } catch (error) {
            console.error('API Error:', error);
            throw error;
        }
    }
    /**
   * Log API calls in development
   */ logApiCall(method, endpoint, data) {
        if ("TURBOPACK compile-time truthy", 1) {
            console.log(`🌐 API ${method}:`, endpoint, data ? {
                data
            } : '');
        }
    }
}
class ServiceUtils {
    /**
   * Format date for API calls
   */ static formatDate(date) {
        return date.toISOString();
    }
    /**
   * Parse API date string to Date object
   */ static parseDate(dateString) {
        return new Date(dateString);
    }
    /**
   * Validate email format
   */ static isValidEmail(email) {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return emailRegex.test(email);
    }
    /**
   * Format currency
   */ static formatCurrency(amount, currency = 'USD') {
        return new Intl.NumberFormat('en-US', {
            style: 'currency',
            currency: currency
        }).format(amount);
    }
    /**
   * Debounce function for search inputs
   */ static debounce(func, wait) {
        let timeout;
        return (...args)=>{
            clearTimeout(timeout);
            timeout = setTimeout(()=>func(...args), wait);
        };
    }
    /**
   * Clean undefined values from objects (useful for API params)
   */ static cleanObject(obj) {
        const cleaned = {};
        Object.entries(obj).forEach(([key, value])=>{
            if (value !== undefined && value !== null && value !== '') {
                cleaned[key] = value;
            }
        });
        return cleaned;
    }
}
}}),
"[project]/src/services/api/collections/get.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "CollectionGetService": (()=>CollectionGetService),
    "collectionGetService": (()=>collectionGetService)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$config$2f$baseService$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/services/config/baseService.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$config$2f$apiConfig$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/services/config/apiConfig.ts [app-ssr] (ecmascript)");
;
;
class CollectionGetService extends __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$config$2f$baseService$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["BaseService"] {
    /**
   * Get all collections
   */ async getAll() {
        this.logApiCall('GET', __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$config$2f$apiConfig$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ApiEndpoints"].Collections.Base);
        return this.handleResponse(this.client.get(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$config$2f$apiConfig$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ApiEndpoints"].Collections.Base));
    }
    /**
   * Get collection by ID
   */ async getById(id) {
        this.logApiCall('GET', __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$config$2f$apiConfig$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ApiEndpoints"].Collections.ById(id));
        return this.handleResponse(this.client.get(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$config$2f$apiConfig$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ApiEndpoints"].Collections.ById(id)));
    }
    /**
   * Get collections by level
   */ async getByLevel(level) {
        this.logApiCall('GET', __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$config$2f$apiConfig$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ApiEndpoints"].Collections.ByLevel(level));
        return this.handleResponse(this.client.get(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$config$2f$apiConfig$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ApiEndpoints"].Collections.ByLevel(level)));
    }
    /**
   * Get children of a collection
   */ async getChildren(parentId) {
        this.logApiCall('GET', __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$config$2f$apiConfig$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ApiEndpoints"].Collections.Children(parentId));
        return this.handleResponse(this.client.get(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$config$2f$apiConfig$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ApiEndpoints"].Collections.Children(parentId)));
    }
    /**
   * Get collection hierarchy
   */ async getHierarchy() {
        this.logApiCall('GET', __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$config$2f$apiConfig$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ApiEndpoints"].Collections.Hierarchy);
        return this.handleResponse(this.client.get(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$config$2f$apiConfig$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ApiEndpoints"].Collections.Hierarchy));
    }
    /**
   * Get published collections
   */ async getPublished() {
        this.logApiCall('GET', __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$config$2f$apiConfig$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ApiEndpoints"].Collections.Published);
        return this.handleResponse(this.client.get(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$config$2f$apiConfig$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ApiEndpoints"].Collections.Published));
    }
    /**
   * Search collections by name
   */ async search(name) {
        this.logApiCall('GET', __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$config$2f$apiConfig$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ApiEndpoints"].Collections.Search, {
            name
        });
        return this.handleResponse(this.client.get(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$config$2f$apiConfig$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ApiEndpoints"].Collections.Search, {
            name
        }));
    }
    /**
   * Get collections with advanced filtering and pagination
   */ async getFiltered(filters) {
        const cleanFilters = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$config$2f$baseService$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ServiceUtils"].cleanObject(filters);
        this.logApiCall('GET', __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$config$2f$apiConfig$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ApiEndpoints"].Collections.Filter, cleanFilters);
        return this.handlePaginatedResponse(this.client.get(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$config$2f$apiConfig$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ApiEndpoints"].Collections.Filter, cleanFilters));
    }
    /**
   * Get collections with default pagination
   */ async getPaginated(pageNumber = 1, pageSize = 10, sortBy = 'createdAt', sortDirection = 'desc') {
        const filters = {
            pageNumber,
            pageSize,
            sortBy,
            sortDirection
        };
        return this.getFiltered(filters);
    }
    /**
   * Get root collections (level 1)
   */ async getRootCollections() {
        return this.getByLevel(1);
    }
    /**
   * Get collections by tag
   */ async getByTag(tag) {
        const filters = {
            tag,
            pageSize: 100 // Get all matching collections
        };
        const result = await this.getFiltered(filters);
        return result.data;
    }
    /**
   * Get collections created by specific user
   */ async getByCreatedBy(createdBy) {
        const filters = {
            createdBy,
            pageSize: 100
        };
        const result = await this.getFiltered(filters);
        return result.data;
    }
    /**
   * Get collections created within date range
   */ async getByDateRange(startDate, endDate) {
        const filters = {
            createdAfter: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$config$2f$baseService$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ServiceUtils"].formatDate(startDate),
            createdBefore: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$config$2f$baseService$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ServiceUtils"].formatDate(endDate),
            pageSize: 100
        };
        const result = await this.getFiltered(filters);
        return result.data;
    }
}
const collectionGetService = new CollectionGetService();
}}),
"[project]/src/services/api/collections/post.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "CollectionPostService": (()=>CollectionPostService),
    "collectionPostService": (()=>collectionPostService)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$config$2f$baseService$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/services/config/baseService.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$config$2f$apiConfig$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/services/config/apiConfig.ts [app-ssr] (ecmascript)");
;
;
class CollectionPostService extends __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$config$2f$baseService$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["BaseService"] {
    /**
   * Create a new collection
   */ async create(data) {
        this.logApiCall('POST', __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$config$2f$apiConfig$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ApiEndpoints"].Collections.Base, data);
        // Validate required fields
        this.validateCreateRequest(data);
        return this.handleResponse(this.client.post(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$config$2f$apiConfig$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ApiEndpoints"].Collections.Base, data));
    }
    /**
   * Create a root collection (Level 1)
   */ async createRootCollection(name, description, tags = [], published = false, createdBy) {
        const data = {
            name,
            description,
            level: 1,
            parentCollectionId: undefined,
            childCollectionId: undefined,
            tags,
            published,
            createdBy
        };
        return this.create(data);
    }
    /**
   * Create a sub-collection (Level 2 or 3)
   */ async createSubCollection(name, description, level, parentCollectionId, tags = [], published = false, createdBy) {
        const data = {
            name,
            description,
            level,
            parentCollectionId,
            childCollectionId: undefined,
            tags,
            published,
            createdBy
        };
        return this.create(data);
    }
    /**
   * Create multiple collections in batch
   */ async createBatch(collections) {
        this.logApiCall('POST', 'Batch Collections', {
            count: collections.length
        });
        const promises = collections.map((collection)=>this.create(collection));
        return Promise.all(promises);
    }
    /**
   * Refresh parent-child relationships for all collections (maintenance operation)
   */ async refreshAllRelationships() {
        this.logApiCall('POST', __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$config$2f$apiConfig$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ApiEndpoints"].Collections.RefreshRelationships);
        try {
            const response = await this.client.post(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$config$2f$apiConfig$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ApiEndpoints"].Collections.RefreshRelationships);
            if (response.success && response.data !== undefined) {
                return {
                    success: true,
                    updatedCount: response.data,
                    message: response.message || `Updated ${response.data} collection relationships`
                };
            } else {
                return {
                    success: false,
                    updatedCount: 0,
                    message: response.message || 'Failed to refresh relationships'
                };
            }
        } catch (error) {
            console.error('Error refreshing collection relationships:', error);
            return {
                success: false,
                updatedCount: 0,
                message: error instanceof Error ? error.message : 'Unknown error occurred'
            };
        }
    }
    /**
   * Validate create collection request
   */ validateCreateRequest(data) {
        if (!data.name || data.name.trim().length === 0) {
            throw new Error('Collection name is required');
        }
        if (data.name.length > 200) {
            throw new Error('Collection name must be 200 characters or less');
        }
        if (!data.level || data.level < 1 || data.level > 3) {
            throw new Error('Collection level must be 1, 2, or 3');
        }
        if (data.level === 1 && data.parentCollectionId) {
            throw new Error('Root collections (level 1) cannot have a parent');
        }
        if (data.level > 1 && !data.parentCollectionId) {
            throw new Error('Sub-collections (level 2-3) must have a parent');
        }
        if (!data.createdBy || data.createdBy.trim().length === 0) {
            throw new Error('CreatedBy is required');
        }
        if (data.createdBy.length > 100) {
            throw new Error('CreatedBy must be 100 characters or less');
        }
        if (data.description && data.description.length > 1000) {
            throw new Error('Description must be 1000 characters or less');
        }
    }
}
const collectionPostService = new CollectionPostService();
}}),
"[project]/src/services/api/collections/update.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "CollectionUpdateService": (()=>CollectionUpdateService),
    "collectionUpdateService": (()=>collectionUpdateService)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$config$2f$baseService$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/services/config/baseService.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$config$2f$apiConfig$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/services/config/apiConfig.ts [app-ssr] (ecmascript)");
;
;
class CollectionUpdateService extends __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$config$2f$baseService$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["BaseService"] {
    /**
   * Update an existing collection
   */ async update(id, data) {
        this.logApiCall('PUT', __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$config$2f$apiConfig$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ApiEndpoints"].Collections.ById(id), data);
        // Validate required fields
        this.validateUpdateRequest(data);
        return this.handleResponse(this.client.put(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$config$2f$apiConfig$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ApiEndpoints"].Collections.ById(id), data));
    }
    /**
   * Update collection name and description
   */ async updateBasicInfo(id, name, description, updatedBy) {
        // First get the current collection to preserve other fields
        const currentCollection = await this.client.get(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$config$2f$apiConfig$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ApiEndpoints"].Collections.ById(id));
        if (!currentCollection.success || !currentCollection.data) {
            throw new Error('Collection not found');
        }
        const data = {
            name,
            description,
            level: currentCollection.data.level,
            parentCollectionId: currentCollection.data.parentCollectionId,
            childCollectionId: currentCollection.data.childCollectionId,
            tags: currentCollection.data.tags,
            published: currentCollection.data.published,
            updatedBy
        };
        return this.update(id, data);
    }
    /**
   * Update collection tags
   */ async updateTags(id, tags, updatedBy) {
        const currentCollection = await this.client.get(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$config$2f$apiConfig$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ApiEndpoints"].Collections.ById(id));
        if (!currentCollection.success || !currentCollection.data) {
            throw new Error('Collection not found');
        }
        const data = {
            name: currentCollection.data.name,
            description: currentCollection.data.description,
            level: currentCollection.data.level,
            parentCollectionId: currentCollection.data.parentCollectionId,
            childCollectionId: currentCollection.data.childCollectionId,
            tags,
            published: currentCollection.data.published,
            updatedBy
        };
        return this.update(id, data);
    }
    /**
   * Publish or unpublish a collection
   */ async updatePublishStatus(id, published, updatedBy) {
        const currentCollection = await this.client.get(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$config$2f$apiConfig$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ApiEndpoints"].Collections.ById(id));
        if (!currentCollection.success || !currentCollection.data) {
            throw new Error('Collection not found');
        }
        const data = {
            name: currentCollection.data.name,
            description: currentCollection.data.description,
            level: currentCollection.data.level,
            parentCollectionId: currentCollection.data.parentCollectionId,
            childCollectionId: currentCollection.data.childCollectionId,
            tags: currentCollection.data.tags,
            published,
            updatedBy
        };
        return this.update(id, data);
    }
    /**
   * Move collection to different parent (change hierarchy)
   */ async moveToParent(id, newParentId, newLevel, updatedBy) {
        const currentCollection = await this.client.get(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$config$2f$apiConfig$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ApiEndpoints"].Collections.ById(id));
        if (!currentCollection.success || !currentCollection.data) {
            throw new Error('Collection not found');
        }
        const data = {
            name: currentCollection.data.name,
            description: currentCollection.data.description,
            level: newLevel,
            parentCollectionId: newParentId,
            childCollectionId: currentCollection.data.childCollectionId,
            tags: currentCollection.data.tags,
            published: currentCollection.data.published,
            updatedBy
        };
        return this.update(id, data);
    }
    /**
   * Add tags to existing collection
   */ async addTags(id, newTags, updatedBy) {
        const currentCollection = await this.client.get(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$config$2f$apiConfig$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ApiEndpoints"].Collections.ById(id));
        if (!currentCollection.success || !currentCollection.data) {
            throw new Error('Collection not found');
        }
        const existingTags = currentCollection.data.tags || [];
        const uniqueTags = [
            ...new Set([
                ...existingTags,
                ...newTags
            ])
        ];
        return this.updateTags(id, uniqueTags, updatedBy);
    }
    /**
   * Remove tags from existing collection
   */ async removeTags(id, tagsToRemove, updatedBy) {
        const currentCollection = await this.client.get(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$config$2f$apiConfig$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ApiEndpoints"].Collections.ById(id));
        if (!currentCollection.success || !currentCollection.data) {
            throw new Error('Collection not found');
        }
        const existingTags = currentCollection.data.tags || [];
        const filteredTags = existingTags.filter((tag)=>!tagsToRemove.includes(tag));
        return this.updateTags(id, filteredTags, updatedBy);
    }
    /**
   * Validate update collection request
   */ validateUpdateRequest(data) {
        if (!data.name || data.name.trim().length === 0) {
            throw new Error('Collection name is required');
        }
        if (data.name.length > 200) {
            throw new Error('Collection name must be 200 characters or less');
        }
        if (!data.level || data.level < 1 || data.level > 3) {
            throw new Error('Collection level must be 1, 2, or 3');
        }
        if (data.level === 1 && data.parentCollectionId) {
            throw new Error('Root collections (level 1) cannot have a parent');
        }
        if (data.level > 1 && !data.parentCollectionId) {
            throw new Error('Sub-collections (level 2-3) must have a parent');
        }
        if (!data.updatedBy || data.updatedBy.trim().length === 0) {
            throw new Error('UpdatedBy is required');
        }
        if (data.updatedBy.length > 100) {
            throw new Error('UpdatedBy must be 100 characters or less');
        }
        if (data.description && data.description.length > 1000) {
            throw new Error('Description must be 1000 characters or less');
        }
    }
}
const collectionUpdateService = new CollectionUpdateService();
}}),
"[project]/src/services/api/collections/delete.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "CollectionDeleteService": (()=>CollectionDeleteService),
    "collectionDeleteService": (()=>collectionDeleteService)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$config$2f$baseService$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/services/config/baseService.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$config$2f$apiConfig$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/services/config/apiConfig.ts [app-ssr] (ecmascript)");
;
;
class CollectionDeleteService extends __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$config$2f$baseService$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["BaseService"] {
    /**
   * Delete a collection by ID
   */ async delete(id) {
        this.logApiCall('DELETE', __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$config$2f$apiConfig$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ApiEndpoints"].Collections.ById(id));
        return this.handleVoidResponse(this.client.delete(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$config$2f$apiConfig$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ApiEndpoints"].Collections.ById(id)));
    }
    /**
   * Delete multiple collections
   */ async deleteBatch(ids) {
        this.logApiCall('DELETE', 'Batch Collections', {
            count: ids.length
        });
        const promises = ids.map((id)=>this.delete(id));
        return Promise.all(promises);
    }
    /**
   * Soft delete - unpublish collection instead of deleting
   */ async unpublish(id, updatedBy) {
        this.logApiCall('PATCH', `Unpublish Collection ${id}`);
        try {
            // Import here to avoid circular dependency
            const { collectionUpdateService } = await __turbopack_context__.r("[project]/src/services/api/collections/update.ts [app-ssr] (ecmascript, async loader)")(__turbopack_context__.i);
            await collectionUpdateService.updatePublishStatus(id, false, updatedBy);
            return true;
        } catch (error) {
            console.error('Failed to unpublish collection:', error);
            return false;
        }
    }
    /**
   * Check if collection can be safely deleted
   */ async canDelete(id) {
        try {
            // Import here to avoid circular dependency
            const { collectionGetService } = await __turbopack_context__.r("[project]/src/services/api/collections/get.ts [app-ssr] (ecmascript, async loader)")(__turbopack_context__.i);
            // Check if collection has children
            const children = await collectionGetService.getChildren(id);
            const hasChildren = children.length > 0;
            // Get collection with products to check if it has products
            const collection = await collectionGetService.getById(id);
            const hasProducts = collection.products && collection.products.length > 0;
            const canDelete = !hasChildren && !hasProducts;
            let reason;
            if (!canDelete) {
                if (hasChildren && hasProducts) {
                    reason = 'Collection has both child collections and products';
                } else if (hasChildren) {
                    reason = 'Collection has child collections';
                } else if (hasProducts) {
                    reason = 'Collection has products';
                }
            }
            return {
                canDelete,
                reason,
                hasChildren,
                hasProducts
            };
        } catch (error) {
            console.error('Error checking if collection can be deleted:', error);
            return {
                canDelete: false,
                reason: 'Error checking collection dependencies'
            };
        }
    }
    /**
   * Safe delete - checks dependencies before deleting
   */ async safeDelete(id) {
        try {
            const deleteCheck = await this.canDelete(id);
            if (!deleteCheck.canDelete) {
                return {
                    success: false,
                    message: deleteCheck.reason || 'Collection cannot be deleted'
                };
            }
            const deleted = await this.delete(id);
            if (deleted) {
                return {
                    success: true,
                    message: 'Collection deleted successfully'
                };
            } else {
                return {
                    success: false,
                    message: 'Failed to delete collection'
                };
            }
        } catch (error) {
            console.error('Error during safe delete:', error);
            return {
                success: false,
                message: error instanceof Error ? error.message : 'Unknown error occurred'
            };
        }
    }
    /**
   * Force delete with cascade (delete children and move products)
   * Note: This should be used with extreme caution
   */ async forceDelete(id, moveProductsToCollectionId) {
        try {
            const { collectionGetService } = await __turbopack_context__.r("[project]/src/services/api/collections/get.ts [app-ssr] (ecmascript, async loader)")(__turbopack_context__.i);
            const deletedCollections = [];
            let movedProducts = 0;
            // Get collection details
            const collection = await collectionGetService.getById(id);
            // Move products to another collection if specified
            if (moveProductsToCollectionId && collection.products.length > 0) {
                // This would require product service - placeholder for now
                movedProducts = collection.products.length;
                console.warn('Product moving not implemented - would move', movedProducts, 'products');
            }
            // Delete children recursively
            const children = await collectionGetService.getChildren(id);
            for (const child of children){
                const childResult = await this.forceDelete(child.id, moveProductsToCollectionId);
                if (childResult.success && childResult.deletedCollections) {
                    deletedCollections.push(...childResult.deletedCollections);
                }
            }
            // Delete the collection itself
            const deleted = await this.delete(id);
            if (deleted) {
                deletedCollections.push(id);
            }
            return {
                success: deleted,
                message: deleted ? `Successfully deleted collection and ${deletedCollections.length - 1} child collections` : 'Failed to delete collection',
                deletedCollections,
                movedProducts
            };
        } catch (error) {
            console.error('Error during force delete:', error);
            return {
                success: false,
                message: error instanceof Error ? error.message : 'Unknown error occurred'
            };
        }
    }
}
const collectionDeleteService = new CollectionDeleteService();
}}),
"[project]/src/services/api/collections/index.ts [app-ssr] (ecmascript) <locals>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
// Collection Services
__turbopack_context__.s({
    "CollectionService": (()=>CollectionService),
    "collectionService": (()=>collectionService)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$api$2f$collections$2f$get$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/services/api/collections/get.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$api$2f$collections$2f$post$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/services/api/collections/post.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$api$2f$collections$2f$update$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/services/api/collections/update.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$api$2f$collections$2f$delete$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/services/api/collections/delete.ts [app-ssr] (ecmascript)");
;
;
;
;
;
;
;
;
class CollectionService {
    get = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$api$2f$collections$2f$get$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["collectionGetService"];
    post = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$api$2f$collections$2f$post$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["collectionPostService"];
    update = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$api$2f$collections$2f$update$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["collectionUpdateService"];
    delete = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$api$2f$collections$2f$delete$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["collectionDeleteService"];
}
const collectionService = new CollectionService();
}}),
"[project]/src/services/api/collections/index.ts [app-ssr] (ecmascript) <module evaluation>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$api$2f$collections$2f$get$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/services/api/collections/get.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$api$2f$collections$2f$post$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/services/api/collections/post.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$api$2f$collections$2f$update$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/services/api/collections/update.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$api$2f$collections$2f$delete$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/services/api/collections/delete.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$api$2f$collections$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/src/services/api/collections/index.ts [app-ssr] (ecmascript) <locals>");
}}),
"[project]/src/components/shared/Header/header.module.css [app-ssr] (css module)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v({
  "active": "header-module__zhIT0W__active",
  "cartContainer": "header-module__zhIT0W__cartContainer",
  "cartLink": "header-module__zhIT0W__cartLink",
  "container": "header-module__zhIT0W__container",
  "dropdown": "header-module__zhIT0W__dropdown",
  "dropdownContainer": "header-module__zhIT0W__dropdownContainer",
  "dropdownIcon": "header-module__zhIT0W__dropdownIcon",
  "dropdownItem": "header-module__zhIT0W__dropdownItem",
  "dropdownLink": "header-module__zhIT0W__dropdownLink",
  "dropdownList": "header-module__zhIT0W__dropdownList",
  "dropdownSlideIn": "header-module__zhIT0W__dropdownSlideIn",
  "header": "header-module__zhIT0W__header",
  "loadingIcon": "header-module__zhIT0W__loadingIcon",
  "logo": "header-module__zhIT0W__logo",
  "logoLink": "header-module__zhIT0W__logoLink",
  "logoSubtext": "header-module__zhIT0W__logoSubtext",
  "logoText": "header-module__zhIT0W__logoText",
  "nav": "header-module__zhIT0W__nav",
  "navButton": "header-module__zhIT0W__navButton",
  "navItem": "header-module__zhIT0W__navItem",
  "navLink": "header-module__zhIT0W__navLink",
  "navList": "header-module__zhIT0W__navList",
  "rotated": "header-module__zhIT0W__rotated",
  "subDropdownItem": "header-module__zhIT0W__subDropdownItem",
  "subDropdownLink": "header-module__zhIT0W__subDropdownLink",
  "subDropdownList": "header-module__zhIT0W__subDropdownList",
});
}}),
"[project]/src/components/shared/Header/Header.tsx [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/client/app-dir/link.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$api$2f$collections$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/src/services/api/collections/index.ts [app-ssr] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$api$2f$collections$2f$get$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/services/api/collections/get.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$shared$2f$Header$2f$header$2e$module$2e$css__$5b$app$2d$ssr$5d$__$28$css__module$29$__ = __turbopack_context__.i("[project]/src/components/shared/Header/header.module.css [app-ssr] (css module)");
'use client';
;
;
;
;
;
const Header = ({ title = "Cast Stone" })=>{
    const [collections, setCollections] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])([]);
    const [activeDropdown, setActiveDropdown] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(null);
    const [isLoading, setIsLoading] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(false);
    const dropdownRefs = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useRef"])({});
    // Company dropdown items
    const companyItems = [
        {
            label: 'Contact Us',
            href: '/contact'
        },
        {
            label: 'Our Story',
            href: '/our-story'
        },
        {
            label: 'Retail Locator',
            href: '/retail-locator'
        },
        {
            label: 'Wholesale Signup',
            href: '/wholesale-signup'
        }
    ];
    // Discover dropdown items
    const discoverItems = [
        {
            label: 'Catalog',
            href: '/catalog'
        },
        {
            label: 'Finishes',
            href: '/finishes'
        },
        {
            label: 'Videos',
            href: '/videos'
        },
        {
            label: 'Technical Info',
            href: '/technical-info'
        },
        {
            label: 'FAQ',
            href: '/faq'
        }
    ];
    // Fetch collections on component mount
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        const fetchCollections = async ()=>{
            try {
                setIsLoading(true);
                const hierarchyData = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$api$2f$collections$2f$get$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["collectionGetService"].getHierarchy();
                setCollections(hierarchyData);
            } catch (error) {
                console.error('Failed to fetch collections:', error);
            } finally{
                setIsLoading(false);
            }
        };
        fetchCollections();
    }, []);
    // Handle dropdown toggle
    const handleDropdownToggle = (dropdownName)=>{
        setActiveDropdown(activeDropdown === dropdownName ? null : dropdownName);
    };
    // Handle click outside to close dropdown
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        const handleClickOutside = (event)=>{
            const target = event.target;
            const isClickInsideDropdown = Object.values(dropdownRefs.current).some((ref)=>ref && ref.contains(target));
            if (!isClickInsideDropdown) {
                setActiveDropdown(null);
            }
        };
        document.addEventListener('mousedown', handleClickOutside);
        return ()=>document.removeEventListener('mousedown', handleClickOutside);
    }, []);
    // Convert collections to dropdown items
    const collectionsToDropdownItems = (collections)=>{
        return collections.map((collection)=>({
                label: collection.name,
                href: `/collections/${collection.id}`,
                children: collection.children.length > 0 ? collectionsToDropdownItems(collection.children) : undefined
            }));
    };
    const collectionItems = collectionsToDropdownItems(collections);
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("header", {
        className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$shared$2f$Header$2f$header$2e$module$2e$css__$5b$app$2d$ssr$5d$__$28$css__module$29$__["default"].header,
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
            className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$shared$2f$Header$2f$header$2e$module$2e$css__$5b$app$2d$ssr$5d$__$28$css__module$29$__["default"].container,
            children: [
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$shared$2f$Header$2f$header$2e$module$2e$css__$5b$app$2d$ssr$5d$__$28$css__module$29$__["default"].logo,
                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
                        href: "/",
                        className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$shared$2f$Header$2f$header$2e$module$2e$css__$5b$app$2d$ssr$5d$__$28$css__module$29$__["default"].logoLink,
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$shared$2f$Header$2f$header$2e$module$2e$css__$5b$app$2d$ssr$5d$__$28$css__module$29$__["default"].logoText,
                                children: title
                            }, void 0, false, {
                                fileName: "[project]/src/components/shared/Header/Header.tsx",
                                lineNumber: 95,
                                columnNumber: 13
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$shared$2f$Header$2f$header$2e$module$2e$css__$5b$app$2d$ssr$5d$__$28$css__module$29$__["default"].logoSubtext,
                                children: "Interiors & Decorations"
                            }, void 0, false, {
                                fileName: "[project]/src/components/shared/Header/Header.tsx",
                                lineNumber: 96,
                                columnNumber: 13
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/shared/Header/Header.tsx",
                        lineNumber: 94,
                        columnNumber: 11
                    }, this)
                }, void 0, false, {
                    fileName: "[project]/src/components/shared/Header/Header.tsx",
                    lineNumber: 93,
                    columnNumber: 9
                }, this),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("nav", {
                    className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$shared$2f$Header$2f$header$2e$module$2e$css__$5b$app$2d$ssr$5d$__$28$css__module$29$__["default"].nav,
                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("ul", {
                        className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$shared$2f$Header$2f$header$2e$module$2e$css__$5b$app$2d$ssr$5d$__$28$css__module$29$__["default"].navList,
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("li", {
                                className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$shared$2f$Header$2f$header$2e$module$2e$css__$5b$app$2d$ssr$5d$__$28$css__module$29$__["default"].navItem,
                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$shared$2f$Header$2f$header$2e$module$2e$css__$5b$app$2d$ssr$5d$__$28$css__module$29$__["default"].dropdownContainer,
                                    ref: (el)=>dropdownRefs.current['company'] = el,
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                            className: `${__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$shared$2f$Header$2f$header$2e$module$2e$css__$5b$app$2d$ssr$5d$__$28$css__module$29$__["default"].navButton} ${activeDropdown === 'company' ? __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$shared$2f$Header$2f$header$2e$module$2e$css__$5b$app$2d$ssr$5d$__$28$css__module$29$__["default"].active : ''}`,
                                            onClick: ()=>handleDropdownToggle('company'),
                                            "aria-expanded": activeDropdown === 'company',
                                            children: [
                                                "Company",
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                    className: `${__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$shared$2f$Header$2f$header$2e$module$2e$css__$5b$app$2d$ssr$5d$__$28$css__module$29$__["default"].dropdownIcon} ${activeDropdown === 'company' ? __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$shared$2f$Header$2f$header$2e$module$2e$css__$5b$app$2d$ssr$5d$__$28$css__module$29$__["default"].rotated : ''}`,
                                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("svg", {
                                                        width: "12",
                                                        height: "8",
                                                        viewBox: "0 0 12 8",
                                                        fill: "none",
                                                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                                                            d: "M1 1.5L6 6.5L11 1.5",
                                                            stroke: "currentColor",
                                                            strokeWidth: "1.5",
                                                            strokeLinecap: "round",
                                                            strokeLinejoin: "round"
                                                        }, void 0, false, {
                                                            fileName: "[project]/src/components/shared/Header/Header.tsx",
                                                            lineNumber: 117,
                                                            columnNumber: 23
                                                        }, this)
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/components/shared/Header/Header.tsx",
                                                        lineNumber: 116,
                                                        columnNumber: 21
                                                    }, this)
                                                }, void 0, false, {
                                                    fileName: "[project]/src/components/shared/Header/Header.tsx",
                                                    lineNumber: 115,
                                                    columnNumber: 19
                                                }, this)
                                            ]
                                        }, void 0, true, {
                                            fileName: "[project]/src/components/shared/Header/Header.tsx",
                                            lineNumber: 109,
                                            columnNumber: 17
                                        }, this),
                                        activeDropdown === 'company' && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$shared$2f$Header$2f$header$2e$module$2e$css__$5b$app$2d$ssr$5d$__$28$css__module$29$__["default"].dropdown,
                                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("ul", {
                                                className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$shared$2f$Header$2f$header$2e$module$2e$css__$5b$app$2d$ssr$5d$__$28$css__module$29$__["default"].dropdownList,
                                                children: companyItems.map((item, index)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("li", {
                                                        className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$shared$2f$Header$2f$header$2e$module$2e$css__$5b$app$2d$ssr$5d$__$28$css__module$29$__["default"].dropdownItem,
                                                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
                                                            href: item.href,
                                                            className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$shared$2f$Header$2f$header$2e$module$2e$css__$5b$app$2d$ssr$5d$__$28$css__module$29$__["default"].dropdownLink,
                                                            children: item.label
                                                        }, void 0, false, {
                                                            fileName: "[project]/src/components/shared/Header/Header.tsx",
                                                            lineNumber: 126,
                                                            columnNumber: 27
                                                        }, this)
                                                    }, index, false, {
                                                        fileName: "[project]/src/components/shared/Header/Header.tsx",
                                                        lineNumber: 125,
                                                        columnNumber: 25
                                                    }, this))
                                            }, void 0, false, {
                                                fileName: "[project]/src/components/shared/Header/Header.tsx",
                                                lineNumber: 123,
                                                columnNumber: 21
                                            }, this)
                                        }, void 0, false, {
                                            fileName: "[project]/src/components/shared/Header/Header.tsx",
                                            lineNumber: 122,
                                            columnNumber: 19
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/src/components/shared/Header/Header.tsx",
                                    lineNumber: 105,
                                    columnNumber: 15
                                }, this)
                            }, void 0, false, {
                                fileName: "[project]/src/components/shared/Header/Header.tsx",
                                lineNumber: 104,
                                columnNumber: 13
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("li", {
                                className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$shared$2f$Header$2f$header$2e$module$2e$css__$5b$app$2d$ssr$5d$__$28$css__module$29$__["default"].navItem,
                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
                                    href: "/products",
                                    className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$shared$2f$Header$2f$header$2e$module$2e$css__$5b$app$2d$ssr$5d$__$28$css__module$29$__["default"].navLink,
                                    children: "Products"
                                }, void 0, false, {
                                    fileName: "[project]/src/components/shared/Header/Header.tsx",
                                    lineNumber: 139,
                                    columnNumber: 15
                                }, this)
                            }, void 0, false, {
                                fileName: "[project]/src/components/shared/Header/Header.tsx",
                                lineNumber: 138,
                                columnNumber: 13
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("li", {
                                className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$shared$2f$Header$2f$header$2e$module$2e$css__$5b$app$2d$ssr$5d$__$28$css__module$29$__["default"].navItem,
                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$shared$2f$Header$2f$header$2e$module$2e$css__$5b$app$2d$ssr$5d$__$28$css__module$29$__["default"].dropdownContainer,
                                    ref: (el)=>dropdownRefs.current['collections'] = el,
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                            className: `${__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$shared$2f$Header$2f$header$2e$module$2e$css__$5b$app$2d$ssr$5d$__$28$css__module$29$__["default"].navButton} ${activeDropdown === 'collections' ? __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$shared$2f$Header$2f$header$2e$module$2e$css__$5b$app$2d$ssr$5d$__$28$css__module$29$__["default"].active : ''}`,
                                            onClick: ()=>handleDropdownToggle('collections'),
                                            "aria-expanded": activeDropdown === 'collections',
                                            disabled: isLoading,
                                            children: [
                                                "Collections",
                                                isLoading ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                    className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$shared$2f$Header$2f$header$2e$module$2e$css__$5b$app$2d$ssr$5d$__$28$css__module$29$__["default"].loadingIcon,
                                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("svg", {
                                                        width: "12",
                                                        height: "12",
                                                        viewBox: "0 0 24 24",
                                                        fill: "none",
                                                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("circle", {
                                                            cx: "12",
                                                            cy: "12",
                                                            r: "10",
                                                            stroke: "currentColor",
                                                            strokeWidth: "2",
                                                            strokeLinecap: "round",
                                                            strokeDasharray: "31.416",
                                                            strokeDashoffset: "31.416",
                                                            children: [
                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("animate", {
                                                                    attributeName: "stroke-dasharray",
                                                                    dur: "2s",
                                                                    values: "0 31.416;15.708 15.708;0 31.416",
                                                                    repeatCount: "indefinite"
                                                                }, void 0, false, {
                                                                    fileName: "[project]/src/components/shared/Header/Header.tsx",
                                                                    lineNumber: 161,
                                                                    columnNumber: 27
                                                                }, this),
                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("animate", {
                                                                    attributeName: "stroke-dashoffset",
                                                                    dur: "2s",
                                                                    values: "0;-15.708;-31.416",
                                                                    repeatCount: "indefinite"
                                                                }, void 0, false, {
                                                                    fileName: "[project]/src/components/shared/Header/Header.tsx",
                                                                    lineNumber: 162,
                                                                    columnNumber: 27
                                                                }, this)
                                                            ]
                                                        }, void 0, true, {
                                                            fileName: "[project]/src/components/shared/Header/Header.tsx",
                                                            lineNumber: 160,
                                                            columnNumber: 25
                                                        }, this)
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/components/shared/Header/Header.tsx",
                                                        lineNumber: 159,
                                                        columnNumber: 23
                                                    }, this)
                                                }, void 0, false, {
                                                    fileName: "[project]/src/components/shared/Header/Header.tsx",
                                                    lineNumber: 158,
                                                    columnNumber: 21
                                                }, this) : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                    className: `${__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$shared$2f$Header$2f$header$2e$module$2e$css__$5b$app$2d$ssr$5d$__$28$css__module$29$__["default"].dropdownIcon} ${activeDropdown === 'collections' ? __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$shared$2f$Header$2f$header$2e$module$2e$css__$5b$app$2d$ssr$5d$__$28$css__module$29$__["default"].rotated : ''}`,
                                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("svg", {
                                                        width: "12",
                                                        height: "8",
                                                        viewBox: "0 0 12 8",
                                                        fill: "none",
                                                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                                                            d: "M1 1.5L6 6.5L11 1.5",
                                                            stroke: "currentColor",
                                                            strokeWidth: "1.5",
                                                            strokeLinecap: "round",
                                                            strokeLinejoin: "round"
                                                        }, void 0, false, {
                                                            fileName: "[project]/src/components/shared/Header/Header.tsx",
                                                            lineNumber: 169,
                                                            columnNumber: 25
                                                        }, this)
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/components/shared/Header/Header.tsx",
                                                        lineNumber: 168,
                                                        columnNumber: 23
                                                    }, this)
                                                }, void 0, false, {
                                                    fileName: "[project]/src/components/shared/Header/Header.tsx",
                                                    lineNumber: 167,
                                                    columnNumber: 21
                                                }, this)
                                            ]
                                        }, void 0, true, {
                                            fileName: "[project]/src/components/shared/Header/Header.tsx",
                                            lineNumber: 150,
                                            columnNumber: 17
                                        }, this),
                                        activeDropdown === 'collections' && !isLoading && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$shared$2f$Header$2f$header$2e$module$2e$css__$5b$app$2d$ssr$5d$__$28$css__module$29$__["default"].dropdown,
                                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("ul", {
                                                className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$shared$2f$Header$2f$header$2e$module$2e$css__$5b$app$2d$ssr$5d$__$28$css__module$29$__["default"].dropdownList,
                                                children: collectionItems.map((item, index)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("li", {
                                                        className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$shared$2f$Header$2f$header$2e$module$2e$css__$5b$app$2d$ssr$5d$__$28$css__module$29$__["default"].dropdownItem,
                                                        children: [
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
                                                                href: item.href,
                                                                className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$shared$2f$Header$2f$header$2e$module$2e$css__$5b$app$2d$ssr$5d$__$28$css__module$29$__["default"].dropdownLink,
                                                                children: item.label
                                                            }, void 0, false, {
                                                                fileName: "[project]/src/components/shared/Header/Header.tsx",
                                                                lineNumber: 179,
                                                                columnNumber: 27
                                                            }, this),
                                                            item.children && item.children.length > 0 && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("ul", {
                                                                className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$shared$2f$Header$2f$header$2e$module$2e$css__$5b$app$2d$ssr$5d$__$28$css__module$29$__["default"].subDropdownList,
                                                                children: item.children.map((child, childIndex)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("li", {
                                                                        className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$shared$2f$Header$2f$header$2e$module$2e$css__$5b$app$2d$ssr$5d$__$28$css__module$29$__["default"].subDropdownItem,
                                                                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
                                                                            href: child.href,
                                                                            className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$shared$2f$Header$2f$header$2e$module$2e$css__$5b$app$2d$ssr$5d$__$28$css__module$29$__["default"].subDropdownLink,
                                                                            children: child.label
                                                                        }, void 0, false, {
                                                                            fileName: "[project]/src/components/shared/Header/Header.tsx",
                                                                            lineNumber: 186,
                                                                            columnNumber: 35
                                                                        }, this)
                                                                    }, childIndex, false, {
                                                                        fileName: "[project]/src/components/shared/Header/Header.tsx",
                                                                        lineNumber: 185,
                                                                        columnNumber: 33
                                                                    }, this))
                                                            }, void 0, false, {
                                                                fileName: "[project]/src/components/shared/Header/Header.tsx",
                                                                lineNumber: 183,
                                                                columnNumber: 29
                                                            }, this)
                                                        ]
                                                    }, index, true, {
                                                        fileName: "[project]/src/components/shared/Header/Header.tsx",
                                                        lineNumber: 178,
                                                        columnNumber: 25
                                                    }, this))
                                            }, void 0, false, {
                                                fileName: "[project]/src/components/shared/Header/Header.tsx",
                                                lineNumber: 176,
                                                columnNumber: 21
                                            }, this)
                                        }, void 0, false, {
                                            fileName: "[project]/src/components/shared/Header/Header.tsx",
                                            lineNumber: 175,
                                            columnNumber: 19
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/src/components/shared/Header/Header.tsx",
                                    lineNumber: 146,
                                    columnNumber: 15
                                }, this)
                            }, void 0, false, {
                                fileName: "[project]/src/components/shared/Header/Header.tsx",
                                lineNumber: 145,
                                columnNumber: 13
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("li", {
                                className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$shared$2f$Header$2f$header$2e$module$2e$css__$5b$app$2d$ssr$5d$__$28$css__module$29$__["default"].navItem,
                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
                                    href: "/completed-projects",
                                    className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$shared$2f$Header$2f$header$2e$module$2e$css__$5b$app$2d$ssr$5d$__$28$css__module$29$__["default"].navLink,
                                    children: "Completed Projects"
                                }, void 0, false, {
                                    fileName: "[project]/src/components/shared/Header/Header.tsx",
                                    lineNumber: 203,
                                    columnNumber: 15
                                }, this)
                            }, void 0, false, {
                                fileName: "[project]/src/components/shared/Header/Header.tsx",
                                lineNumber: 202,
                                columnNumber: 13
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("li", {
                                className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$shared$2f$Header$2f$header$2e$module$2e$css__$5b$app$2d$ssr$5d$__$28$css__module$29$__["default"].navItem,
                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$shared$2f$Header$2f$header$2e$module$2e$css__$5b$app$2d$ssr$5d$__$28$css__module$29$__["default"].dropdownContainer,
                                    ref: (el)=>dropdownRefs.current['discover'] = el,
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                            className: `${__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$shared$2f$Header$2f$header$2e$module$2e$css__$5b$app$2d$ssr$5d$__$28$css__module$29$__["default"].navButton} ${activeDropdown === 'discover' ? __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$shared$2f$Header$2f$header$2e$module$2e$css__$5b$app$2d$ssr$5d$__$28$css__module$29$__["default"].active : ''}`,
                                            onClick: ()=>handleDropdownToggle('discover'),
                                            "aria-expanded": activeDropdown === 'discover',
                                            children: [
                                                "Discover",
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                    className: `${__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$shared$2f$Header$2f$header$2e$module$2e$css__$5b$app$2d$ssr$5d$__$28$css__module$29$__["default"].dropdownIcon} ${activeDropdown === 'discover' ? __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$shared$2f$Header$2f$header$2e$module$2e$css__$5b$app$2d$ssr$5d$__$28$css__module$29$__["default"].rotated : ''}`,
                                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("svg", {
                                                        width: "12",
                                                        height: "8",
                                                        viewBox: "0 0 12 8",
                                                        fill: "none",
                                                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                                                            d: "M1 1.5L6 6.5L11 1.5",
                                                            stroke: "currentColor",
                                                            strokeWidth: "1.5",
                                                            strokeLinecap: "round",
                                                            strokeLinejoin: "round"
                                                        }, void 0, false, {
                                                            fileName: "[project]/src/components/shared/Header/Header.tsx",
                                                            lineNumber: 222,
                                                            columnNumber: 23
                                                        }, this)
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/components/shared/Header/Header.tsx",
                                                        lineNumber: 221,
                                                        columnNumber: 21
                                                    }, this)
                                                }, void 0, false, {
                                                    fileName: "[project]/src/components/shared/Header/Header.tsx",
                                                    lineNumber: 220,
                                                    columnNumber: 19
                                                }, this)
                                            ]
                                        }, void 0, true, {
                                            fileName: "[project]/src/components/shared/Header/Header.tsx",
                                            lineNumber: 214,
                                            columnNumber: 17
                                        }, this),
                                        activeDropdown === 'discover' && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$shared$2f$Header$2f$header$2e$module$2e$css__$5b$app$2d$ssr$5d$__$28$css__module$29$__["default"].dropdown,
                                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("ul", {
                                                className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$shared$2f$Header$2f$header$2e$module$2e$css__$5b$app$2d$ssr$5d$__$28$css__module$29$__["default"].dropdownList,
                                                children: discoverItems.map((item, index)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("li", {
                                                        className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$shared$2f$Header$2f$header$2e$module$2e$css__$5b$app$2d$ssr$5d$__$28$css__module$29$__["default"].dropdownItem,
                                                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
                                                            href: item.href,
                                                            className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$shared$2f$Header$2f$header$2e$module$2e$css__$5b$app$2d$ssr$5d$__$28$css__module$29$__["default"].dropdownLink,
                                                            children: item.label
                                                        }, void 0, false, {
                                                            fileName: "[project]/src/components/shared/Header/Header.tsx",
                                                            lineNumber: 231,
                                                            columnNumber: 27
                                                        }, this)
                                                    }, index, false, {
                                                        fileName: "[project]/src/components/shared/Header/Header.tsx",
                                                        lineNumber: 230,
                                                        columnNumber: 25
                                                    }, this))
                                            }, void 0, false, {
                                                fileName: "[project]/src/components/shared/Header/Header.tsx",
                                                lineNumber: 228,
                                                columnNumber: 21
                                            }, this)
                                        }, void 0, false, {
                                            fileName: "[project]/src/components/shared/Header/Header.tsx",
                                            lineNumber: 227,
                                            columnNumber: 19
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/src/components/shared/Header/Header.tsx",
                                    lineNumber: 210,
                                    columnNumber: 15
                                }, this)
                            }, void 0, false, {
                                fileName: "[project]/src/components/shared/Header/Header.tsx",
                                lineNumber: 209,
                                columnNumber: 13
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/shared/Header/Header.tsx",
                        lineNumber: 102,
                        columnNumber: 11
                    }, this)
                }, void 0, false, {
                    fileName: "[project]/src/components/shared/Header/Header.tsx",
                    lineNumber: 101,
                    columnNumber: 9
                }, this),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$shared$2f$Header$2f$header$2e$module$2e$css__$5b$app$2d$ssr$5d$__$28$css__module$29$__["default"].cartContainer,
                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
                        href: "/cart",
                        className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$shared$2f$Header$2f$header$2e$module$2e$css__$5b$app$2d$ssr$5d$__$28$css__module$29$__["default"].cartLink,
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("svg", {
                            width: "24",
                            height: "24",
                            viewBox: "0 0 24 24",
                            fill: "none",
                            stroke: "currentColor",
                            strokeWidth: "1.5",
                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                                d: "M3 3H5L5.4 5M7 13H17L21 5H5.4M7 13L5.4 5M7 13L4.7 15.3C4.3 15.7 4.6 16.5 5.1 16.5H17M17 13V17C17 18.1 16.1 19 15 19H9C7.9 19 7 18.1 7 17V13M17 13H7"
                            }, void 0, false, {
                                fileName: "[project]/src/components/shared/Header/Header.tsx",
                                lineNumber: 248,
                                columnNumber: 15
                            }, this)
                        }, void 0, false, {
                            fileName: "[project]/src/components/shared/Header/Header.tsx",
                            lineNumber: 247,
                            columnNumber: 13
                        }, this)
                    }, void 0, false, {
                        fileName: "[project]/src/components/shared/Header/Header.tsx",
                        lineNumber: 246,
                        columnNumber: 11
                    }, this)
                }, void 0, false, {
                    fileName: "[project]/src/components/shared/Header/Header.tsx",
                    lineNumber: 245,
                    columnNumber: 9
                }, this)
            ]
        }, void 0, true, {
            fileName: "[project]/src/components/shared/Header/Header.tsx",
            lineNumber: 91,
            columnNumber: 7
        }, this)
    }, void 0, false, {
        fileName: "[project]/src/components/shared/Header/Header.tsx",
        lineNumber: 90,
        columnNumber: 5
    }, this);
};
const __TURBOPACK__default__export__ = Header;
}}),

};

//# sourceMappingURL=%5Broot-of-the-server%5D__45ff326a._.js.map